<?php
/**
 * Plugin Name: Badalona Cultura Reserva Espais
 * Plugin URI: https://badalonacultura.cat
 * Description: Un plugin per gestionar les sol·licituds d'espais per a Badalona Cultura
 * Version: 1.0
 * Author: Pimpampum
 * Author URI: https://pimpampum.net
 * Text Domain: bc-space-requests
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('BC_REQUESTS_PLUGIN_FILE', __FILE__);
define('BC_REQUESTS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BC_REQUESTS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BC_REQUESTS_VERSION', '1.0.0');

// Include required files
require_once BC_REQUESTS_PLUGIN_DIR . 'includes/class-bc-requests-roles.php';
require_once BC_REQUESTS_PLUGIN_DIR . 'includes/class-bc-requests-comment-attachments.php';
require_once BC_REQUESTS_PLUGIN_DIR . 'includes/class-bc-requests-calendar.php';
require_once BC_REQUESTS_PLUGIN_DIR . 'includes/pdf/class-bc-requests-pdf.php';
require_once BC_REQUESTS_PLUGIN_DIR . 'includes/class-bc-requests-csv-export.php';

// Initialize roles, comment attachments, calendar, PDF functionality, and CSV export
BC_Requests_Roles::init();
BC_Requests_Comment_Attachments::init();
BC_Requests_Calendar::init();
BC_Requests_PDF::init();
BC_Requests_CSV_Export::init();

class BC_Space_Requests {

    private $option_name = 'bc_space_requests_cf7_id';
    private $max_date_option_name = 'bc_space_requests_max_date';
    private $spaces_option_name = 'bc_space_requests_spaces';
    // Admin email settings
    private $email_subject_option_name = 'bc_space_requests_email_subject';
    private $email_body_option_name = 'bc_space_requests_email_body';
    private $admin_email_option_name = 'bc_space_requests_admin_email';
    // Customer email settings
    private $customer_email_subject_option_name = 'bc_space_requests_customer_email_subject';
    private $customer_email_body_option_name = 'bc_space_requests_customer_email_body';
    private $uploads_dir = 'bc-space-requests';

    public function __construct() {
        // Register custom post type
        add_action('init', array($this, 'register_post_type'));

        // Add admin menu
        add_action('admin_menu', array($this, 'admin_menu'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // Add Contact Form 7 hooks
        add_action('wpcf7_before_send_mail', array($this, 'process_form_submission'));
        add_filter('wpcf7_mail_components', array($this, 'customize_mail_components'), 10, 3);

        // Disable default Contact Form 7 mail sending for our target form
        add_filter('wpcf7_skip_mail', array($this, 'maybe_skip_cf7_mail'), 10, 2);

        // Add custom columns to admin list
        add_filter('manage_bc_request_posts_columns', array($this, 'set_custom_columns'));
        add_action('manage_bc_request_posts_custom_column', array($this, 'custom_column_content'), 10, 2);

        // Add filter for status in admin
        add_action('restrict_manage_posts', array($this, 'add_status_filter'));
        add_filter('parse_query', array($this, 'filter_requests_by_status'));

        // Add meta box for status field
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_data'));

        // Process the comment and file upload from the Add Files meta box
        add_action('save_post', array($this, 'process_add_files_meta_box'));

        // Afegir hooks per la gestió d'arxius
        add_action('init', array($this, 'setup_upload_directory'));

        // Afegir meta box específic per arxius
        add_action('add_meta_boxes', array($this, 'add_files_meta_box'));

        // Add comment form file upload field - try multiple hooks to ensure it appears
        add_action('comment_form_top', array($this, 'add_comment_file_upload_field'), 10, 1);
        add_action('comment_form_before_fields', array($this, 'add_comment_file_upload_field'), 10, 1);
        add_action('comment_form', array($this, 'add_comment_file_upload_field'), 10, 1);
        add_action('comment_form_logged_in_after', array($this, 'include_comment_form_template'), 10, 1);

        // Add a direct hook to the comment form fields
        add_filter('comment_form_field_comment', array($this, 'add_upload_field_after_textarea'));

        // Ensure the comment form has the correct enctype attribute
        add_filter('comment_form_defaults', array($this, 'set_comment_form_enctype'));

        // Add a filter to modify the entire comment form
        add_filter('comment_form_defaults', array($this, 'modify_comment_form_defaults'), 10, 1);

        // Enqueue scripts for comment form
        add_action('wp_enqueue_scripts', array($this, 'enqueue_comment_scripts'));

        // Enqueue front-end scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));

        // Handle comment file uploads
        add_action('comment_post', array($this, 'handle_comment_file_upload'), 10, 3);

        // Display attached files in comments
       // add_filter('comment_text', array($this, 'display_comment_attachments'), 10, 2);

        // File protection is handled by BC_Requests_Comment_Attachments class
        add_action('template_redirect', array($this, 'protect_uploads_access'));

        // Enqueue admin styles
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_styles'));

        // redirigir després d'enviar
        add_action('wpcf7_before_send_mail', array($this, 'redirect_after_submission'));

    }

     /**
     * Configura el directori d'uploads i assegura que existeix
     */
    public function setup_upload_directory() {
        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'] . '/' . $this->uploads_dir;

        // Crear el directori base si no existeix
        if (!file_exists($base_dir)) {
            wp_mkdir_p($base_dir);

            // Crear un fitxer index.php per seguretat
            $index_file = $base_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, '<?php // Silence is golden');
            }
        }

        // Always update the .htaccess file to ensure it has the latest security rules
        $htaccess_file = $base_dir . '/.htaccess';
        $htaccess_content = "# Disable directory browsing\n";
        $htaccess_content .= "Options -Indexes\n\n";

        $htaccess_content .= "# Deny direct access to all files\n";
        $htaccess_content .= "<FilesMatch \".*\">\n";
        $htaccess_content .= "  Order Deny,Allow\n";
        $htaccess_content .= "  Deny from all\n";
        $htaccess_content .= "</FilesMatch>\n\n";

        $htaccess_content .= "# Allow access to index.php\n";
        $htaccess_content .= "<Files \"index.php\">\n";
        $htaccess_content .= "  Order Allow,Deny\n";
        $htaccess_content .= "  Allow from all\n";
        $htaccess_content .= "</Files>\n\n";

        $htaccess_content .= "# PHP handler\n";
        $htaccess_content .= "<IfModule mod_php7.c>\n";
        $htaccess_content .= "  php_flag engine off\n";
        $htaccess_content .= "</IfModule>\n\n";

        $htaccess_content .= "<IfModule mod_rewrite.c>\n";
        $htaccess_content .= "  RewriteEngine On\n";
        $htaccess_content .= "  RewriteBase /\n";
        $htaccess_content .= "  RewriteRule ^.*$ - [F,L]\n";
        $htaccess_content .= "</IfModule>\n";

        file_put_contents($htaccess_file, $htaccess_content);

        // Log that we've updated the .htaccess file
        error_log('BC Requests: Updated .htaccess file in ' . $base_dir);
    }

    /**
     * Procés de tractament del formulari modificat per gestionar arxius
     */
    public function process_form_submission($contact_form) {
        $form_id = $contact_form->id();
        $target_form_id = get_option($this->option_name);

        // Debug logging
        error_log('[BC Requests] Processing form submission. Form ID: ' . $form_id . ', Target ID: ' . $target_form_id);

        // Dump all available form IDs for debugging
        $args = array(
            'post_type' => 'wpcf7_contact_form',
            'posts_per_page' => -1,
        );
        $forms = get_posts($args);
        $form_ids = array();
        foreach ($forms as $form) {
            $form_ids[] = $form->ID;
        }
        error_log('[BC Requests] Available Contact Form 7 IDs: ' . implode(', ', $form_ids));

        // Check if this is our target form - try both string and integer comparison
        // Convert both IDs to integers for comparison to handle different formats
        $form_id_int = intval($form_id);
        $target_form_id_int = intval($target_form_id);

        error_log('[BC Requests] Comparing form IDs (int): ' . $form_id_int . ' vs ' . $target_form_id_int);

        if ($form_id != $target_form_id && $form_id_int != $target_form_id_int) {
            error_log('[BC Requests] Form ID mismatch - skipping processing');
            return;
        }

        error_log('[BC Requests] Form ID match - processing submission');

        // Get form submission data
        $submission = WPCF7_Submission::get_instance();
        if (!$submission) {
            error_log('[BC Requests] No submission instance found');
            return;
        }

        $posted_data = $submission->get_posted_data();
        if (empty($posted_data)) {
            error_log('[BC Requests] No posted data found');
            return;
        }

        // Create content from form data
        $content = '';
        foreach ($posted_data as $key => $value) {
            if (is_array($value)) {
                $value = implode(', ', $value);
            }
            if ($key !== '_wpcf7' && $key !== '_wpcf7_version' && $key !== '_wpcf7_locale' &&
                $key !== '_wpcf7_unit_tag' && $key !== '_wpcf7_container_post') {
                $content .= '<strong>' . esc_html($key) . ':</strong> ' . esc_html($value) . '<br>';
            }
        }

        // Create a new request post with a more descriptive title
        $name = isset($posted_data['your-name']) ? $posted_data['your-name'] : '';
        $nif = isset($posted_data['nif']) ? $posted_data['nif'] : '';

        // Create a title with name and NIF if available, otherwise use a default format
        $title = '';
        if (!empty($name) && !empty($nif)) {
            $title = $name . ' ' . $nif;
        } elseif (!empty($name)) {
            $title = $name . ' #' . time();
        } elseif (!empty($nif)) {
            $title = 'Sol·licitud ' . $nif;
        } else {
            $title = 'Sol·licitud #' . time();
        }

        $new_request = array(
            'post_title'    => $title,
            'post_status'   => 'publish',
            'post_type'     => 'bc_request',
            'post_content'  => $content,
            'meta_input'    => array(
                'bc_request_status' => 'pending',
                'bc_request_data'   => maybe_serialize($posted_data),
                'bc_request_id'     => time() // Store the timestamp as a separate ID for reference
            )
        );

        // Insert the post
        $post_id = wp_insert_post($new_request);

        if (!is_wp_error($post_id)) {
            // Store the post ID in meta for reference
            update_post_meta($post_id, 'bc_request_id', $post_id);
            error_log('[BC Requests] Successfully created request post #' . $post_id);

            // Process uploaded files
            $this->process_uploaded_files($submission, $post_id);
        } else {
            error_log('[BC Requests] Error creating request post: ' . $post_id->get_error_message());
        }
    }

    /**
     * Processa i emmagatzema els arxius adjunts
     */
    private function process_uploaded_files($submission, $post_id) {
        if (!$submission) return;

        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'] . '/' . $this->uploads_dir;

        // Crear directori específic per aquest request
        $request_dir = $base_dir . '/' . $post_id;
        if (!file_exists($request_dir)) {
            wp_mkdir_p($request_dir);

            // Crear un fitxer index.php per seguretat
            $index_file = $request_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, '<?php // Silence is golden');
            }
        }

        // Obtenir els arxius carregats
        $files = $submission->uploaded_files();
        if (empty($files)) {
            error_log('[BC Requests] No uploaded files found');
            return;
        }

        $saved_files = array();

        foreach ($files as $field_name => $file_paths) {
            if (empty($file_paths)) continue;

            // Handle multiple files uploaded through the same field
            foreach ((array) $file_paths as $file_path) {
                if (empty($file_path)) continue;

                // Get file info
                $file_name = basename($file_path);
                $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);

                // Generate safe filename (current time + random string + extension)
                $safe_filename = time() . '-' . wp_generate_password(8, false) . '.' . $file_ext;

                // Destination path
                $destination = $request_dir . '/' . $safe_filename;

                // Copy the file
                if (copy($file_path, $destination)) {
                    // Store file info
                    $saved_files[] = array(
                        'original_name' => $file_name,
                        'saved_name' => $safe_filename,
                        'field_name' => $field_name,
                        'file_path' => $destination,
                        'url' => $upload_dir['baseurl'] . '/' . $this->uploads_dir . '/' . $post_id . '/' . $safe_filename,
                        'mime_type' => mime_content_type($destination)
                    );

                    error_log('[BC Requests] File saved: ' . $destination);
                } else {
                    error_log('[BC Requests] Failed to save file: ' . $file_path . ' to ' . $destination);
                }
            }
        }

        // Save file info to post meta
        if (!empty($saved_files)) {
            update_post_meta($post_id, 'bc_request_files', $saved_files);
        }
    }


    public function register_post_type() {
        $args = array(
            'public' => false,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_menu' => false,
            'query_var' => true,
            'rewrite' => false,
            'capability_type' => 'bc_request',
            'map_meta_cap' => true,
            'has_archive' => false,
            'hierarchical' => false,
            'menu_position' => null,
            'supports' => array('title', 'comments'),
            'capabilities' => array(
                // Meta capabilities
                'edit_post' => 'edit_bc_request',
                'read_post' => 'read_bc_request',
                'delete_post' => 'delete_bc_request',
                // Primitive capabilities
                'edit_posts' => 'edit_bc_requests',
                'edit_others_posts' => 'edit_others_bc_requests',
                'publish_posts' => 'publish_bc_requests',
                'read_private_posts' => 'read_private_bc_requests',
                'delete_posts' => 'delete_bc_requests',
                'delete_private_posts' => 'delete_private_bc_requests',
                'delete_published_posts' => 'delete_published_bc_requests',
                'delete_others_posts' => 'delete_others_bc_requests',
                'edit_private_posts' => 'edit_private_bc_requests',
                'edit_published_posts' => 'edit_published_bc_requests'
            )
        );

        register_post_type('bc_request', $args);
    }

    public function admin_menu() {
        add_menu_page(
            __('Reserva espais', 'bc-space-requests'),
            __('Reserva espais', 'bc-space-requests'),
            BC_Requests_Roles::CAPABILITY,
            'bc-space-requests',
            array($this, 'request_list_page'),
            'dashicons-calendar-alt'
        );

        add_submenu_page(
            'bc-space-requests',
            __('Reserva espais', 'bc-space-requests'),
            __('Reserva espais', 'bc-space-requests'),
            BC_Requests_Roles::CAPABILITY,
            'edit.php?post_type=bc_request'
        );

        // Add Calendar submenu
        add_submenu_page(
            'bc-space-requests',
            __('Calendari', 'bc-space-requests'),
            __('Calendari', 'bc-space-requests'),
            BC_Requests_Roles::CAPABILITY,
            'bc-requests-calendar',
            array($this, 'calendar_page')
        );

        add_submenu_page(
            'bc-space-requests',
            __('Configuració', 'bc-space-requests'),
            __('Configuració', 'bc-space-requests'),
            BC_Requests_Roles::SETTINGS_CAPABILITY, // Use our custom settings capability
            'bc-space-requests-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * Display the calendar page
     */
    public function calendar_page() {
        // Enqueue jQuery UI for draggable/droppable/dialog
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-droppable');
        wp_enqueue_script('jquery-ui-dialog');

        // Enqueue jQuery UI styles
        wp_enqueue_style('wp-jquery-ui-dialog');

        // Enqueue calendar styles
        wp_enqueue_style('bc-requests-calendar', BC_REQUESTS_PLUGIN_URL . 'css/admin/calendar.css');

        // Include the calendar template
        include_once BC_REQUESTS_PLUGIN_DIR . 'templates/calendar.php';
    }

    public function request_list_page() {
        echo '<div class="wrap">';
        echo '<h1>' . __('Sol·licituds d\'Espais', 'bc-space-requests') . '</h1>';
        echo '<p>' . __('Aquesta pàgina redirigeix a la llista de sol·licituds. Si no ets redirigit automàticament, si us plau fes clic', 'bc-space-requests') . ' <a href="' . admin_url('edit.php?post_type=bc_request') . '">' . __('aquí', 'bc-space-requests') . '</a>.</p>';
        echo '</div>';
        ?>
        <script type="text/javascript">
            window.location.href = "<?php echo admin_url('edit.php?post_type=bc_request'); ?>";
        </script>
        <?php
    }

    public function settings_page() {
        // Check if user has the required capability
        if (!BC_Requests_Roles::current_user_can_manage_settings()) {
            wp_die(__('No tens permisos suficients per accedir a aquesta pàgina.', 'bc-space-requests'));
        }
        ?>
        <div class="wrap">
            <h1><?php _e('Configuració de Sol·licituds d\'Espais de Badalona Cultura', 'bc-space-requests'); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('bc_space_requests_settings');
                do_settings_sections('bc-space-requests-settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    public function register_settings() {
        // Register CF7 ID setting
        register_setting(
            'bc_space_requests_settings',
            $this->option_name
        );

        // Register max date setting
        register_setting(
            'bc_space_requests_settings',
            $this->max_date_option_name
        );

        // Register spaces setting
        register_setting(
            'bc_space_requests_settings',
            $this->spaces_option_name
        );

        // Register admin email settings
        register_setting(
            'bc_space_requests_settings',
            $this->email_subject_option_name
        );
        register_setting(
            'bc_space_requests_settings',
            $this->email_body_option_name
        );
        register_setting(
            'bc_space_requests_settings',
            $this->admin_email_option_name
        );

        // Register customer email settings
        register_setting(
            'bc_space_requests_settings',
            $this->customer_email_subject_option_name
        );
        register_setting(
            'bc_space_requests_settings',
            $this->customer_email_body_option_name
        );

        add_settings_section(
            'bc_space_requests_general_section',
            __('Configuració General', 'bc-space-requests'),
            function() {
                echo '<p>' . __('Configura els paràmetres del plugin BC Requests.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings'
        );

        // Contact Form 7 ID field
        add_settings_field(
            'bc_space_requests_cf7_id',
            __('ID de Contact Form 7', 'bc-space-requests'),
            function() {
                $value = get_option($this->option_name);
                echo '<input type="text" name="' . $this->option_name . '" value="' . esc_attr($value) . '" placeholder="p. ex. 123" />';
                echo '<p class="description">' . __('Introdueix l\'ID de Contact Form 7 (numèric). Pots trobar-lo a la pàgina d\'administració de Contact Form 7.', 'bc-space-requests') . '</p>';
                echo '<p class="description">' . __('Nota: Ha de coincidir exactament amb l\'ID del formulari utilitzat al teu formulari de Contact Form 7.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_general_section'
        );

        // Max Date field
        add_settings_field(
            'bc_space_requests_max_date',
            __('Data màxima per als camps de data', 'bc-space-requests'),
            function() {
                $value = get_option($this->max_date_option_name);
                if (empty($value)) {
                    // Default to 30 days from now
                    $default_date = date('Y-m-d', strtotime('+30 days'));
                    $value = $default_date;
                }
                echo '<input type="date" name="' . $this->max_date_option_name . '" value="' . esc_attr($value) . '" />';
                echo '<p class="description">' . __('Data màxima que es pot seleccionar en els camps de data amb la classe "restricted-date". Per defecte són 30 dies a partir d\'avui.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_general_section'
        );

        // Spaces field
        add_settings_field(
            'bc_space_requests_spaces',
            __('Espais disponibles', 'bc-space-requests'),
            function() {
                $value = get_option($this->spaces_option_name);
                $spaces = !empty($value) ? explode(',', $value) : ['Teatre Zorrilla', 'Teatre Margarida Xirgu'];
                $spaces_value = implode(',', $spaces);

                echo '<textarea name="' . $this->spaces_option_name . '" rows="5" cols="50" class="large-text">' . esc_textarea($spaces_value) . '</textarea>';
                echo '<p class="description">' . __('Introdueix els espais disponibles, separats per comes. Aquests estaran disponibles a la vista de calendari.', 'bc-space-requests') . '</p>';
                echo '<p class="description">' . __('Exemple: Teatre Zorrilla,Teatre Margarida Xirgu,...', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_general_section'
        );

        // Add Admin Email Settings Section
        add_settings_section(
            'bc_space_requests_admin_email_section',
            __('Opcions email administrador', 'bc-space-requests'),
            function() {
                echo '<p>' . __('Configureu les notificacions per correu electrònic enviades als administradors quan els usuaris envien una sol·licitud.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings'
        );

        // Add Customer Email Settings Section
        add_settings_section(
            'bc_space_requests_customer_email_section',
            __('Configuració d\'Email per a Clients', 'bc-space-requests'),
            function() {
                echo '<p>' . __('Configura les notificacions per correu electrònic enviades als clients quan envien una sol·licitud.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings'
        );

        // Admin Email Subject field
        add_settings_field(
            'bc_space_requests_email_subject',
            __('Assumpte de l\'Email per a l\'Administrador', 'bc-space-requests'),
            function() {
                $value = get_option($this->email_subject_option_name);
                if (empty($value)) {
                    $value = __('Nova Sol·licitud d\'Espai: [espai-1] - Sol·licitud #[request-id]', 'bc-space-requests');
                }
                echo '<input type="text" name="' . $this->email_subject_option_name . '" value="' . esc_attr($value) . '" class="large-text" />';
                echo '<p class="description">' . __('La línia d\'assumpte del correu electrònic enviat als administradors. Pots utilitzar marcadors com [name], [request-id], etc.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_admin_email_section'
        );

        // Admin Email Body field
        add_settings_field(
            'bc_space_requests_email_body',
            __('Cos de l\'Email per a l\'Administrador', 'bc-space-requests'),
            function() {
                $value = get_option($this->email_body_option_name);
                if (empty($value)) {
                    $value = __("S'ha enviat una nova sol·licitud d'espai.\n\nDetalls de la sol·licitud:\nID: [request-id]\nSol·licitant: [name]\nEmail: [your-email]\nTelèfon: [telefon]\nEntitat: [entitat]\nNIF/CIF: [nif]\nEspai: [espai-1]\nData: [data-acte]\n\nVeure i gestionar aquesta sol·licitud aquí: [link-request]", 'bc-space-requests');
                }
                echo '<textarea name="' . $this->email_body_option_name . '" rows="10" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
                echo '<p class="description">' . __('El cos del correu electrònic enviat als administradors. Pots utilitzar marcadors com [name], [request-id], [link-request], etc.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_admin_email_section'
        );

        // Admin Email field
        add_settings_field(
            'bc_space_requests_admin_email',
            __('Email de l\'Administrador', 'bc-space-requests'),
            function() {
                $value = get_option($this->admin_email_option_name);
                if (empty($value)) {
                    $value = get_option('admin_email'); // Use the site admin email as default
                }
                echo '<input type="email" name="' . $this->admin_email_option_name . '" value="' . esc_attr($value) . '" class="regular-text" />';
                echo '<p class="description">' . __('L\'adreça de correu electrònic que rebrà notificacions sobre noves sol·licituds. Deixa-ho buit per utilitzar l\'email d\'administrador de WordPress per defecte.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_admin_email_section'
        );

        // Customer Email Subject field
        add_settings_field(
            'bc_space_requests_customer_email_subject',
            __('Assumpte de l\'Email per al Client', 'bc-space-requests'),
            function() {
                $value = get_option($this->customer_email_subject_option_name);
                if (empty($value)) {
                    $value = __('La teva sol·licitud d\'espai ha estat rebuda - Sol·licitud #[request-id]', 'bc-space-requests');
                }
                echo '<input type="text" name="' . $this->customer_email_subject_option_name . '" value="' . esc_attr($value) . '" class="large-text" />';
                echo '<p class="description">' . __('La línia d\'assumpte del correu electrònic enviat als clients. Pots utilitzar marcadors com [name], [request-id], etc.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_customer_email_section'
        );

        // Customer Email Body field
        add_settings_field(
            'bc_space_requests_customer_email_body',
            __('Cos de l\'Email per al Client', 'bc-space-requests'),
            function() {
                $value = get_option($this->customer_email_body_option_name);
                if (empty($value)) {
                    $value = __("Benvolgut/da [name],\n\nGràcies per la teva sol·licitud d'espai. Hem rebut la teva sol·licitud i està sent processada.\n\nDetalls de la sol·licitud:\nID de sol·licitud: [request-id]\nEspai: [espai-1]\nData: [data-acte]\n\nEns posarem en contacte amb tu aviat respecte a la teva sol·licitud.\n\nSalutacions cordials,\nBadalona Cultura", 'bc-space-requests');
                }
                echo '<textarea name="' . $this->customer_email_body_option_name . '" rows="10" cols="50" class="large-text">' . esc_textarea($value) . '</textarea>';
                echo '<p class="description">' . __('El cos del correu electrònic enviat als clients. Pots utilitzar marcadors com [name], [request-id], etc.', 'bc-space-requests') . '</p>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_customer_email_section'
        );

        // Add Admin Placeholders Section (collapsible)
        add_settings_field(
            'bc_space_requests_admin_placeholders',
            __('Marcadors Disponibles', 'bc-space-requests'),
            function() {
                // Add collapsible section with placeholders
                echo '<div class="bc-requests-placeholders-toggle" style="margin-bottom: 10px;">';
                echo '<button type="button" class="button" id="bc-requests-toggle-admin-placeholders">' . __('Mostrar/Amagar Marcadors', 'bc-space-requests') . '</button>';
                echo '</div>';

                echo '<div class="bc-requests-admin-placeholders" style="display: none; background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin-top: 10px;">';
                echo '<p>' . __('Pots utilitzar els següents marcadors a l\'assumpte i cos del correu electrònic:', 'bc-space-requests') . '</p>';
                echo '<ul style="list-style-type: disc; margin-left: 20px;">';

                // Load field labels from configuration file
                $field_labels = array();
                $labels_file = BC_REQUESTS_PLUGIN_DIR . 'includes/field-labels.php';

                if (file_exists($labels_file)) {
                    include $labels_file;
                    if (isset($bc_requests_field_labels) && is_array($bc_requests_field_labels)) {
                        $field_labels = $bc_requests_field_labels;
                    }
                }

                // Display available placeholders
                foreach ($field_labels as $field_key => $field_label) {
                    echo '<li><code>[' . esc_html($field_key) . ']</code> - ' . esc_html($field_label) . '</li>';
                }

                // Add special placeholders
                echo '<li><code>[link-request]</code> - ' . __('Enllaç per veure la sol·licitud al tauler', 'bc-space-requests') . '</li>';
                echo '<li><code>[request-id]</code> - ' . __('L\'ID de la sol·licitud', 'bc-space-requests') . '</li>';
                echo '<li><code>[request-title]</code> - ' . __('El títol de la sol·licitud', 'bc-space-requests') . '</li>';
                echo '</ul>';
                echo '</div>';

                // Add JavaScript to toggle placeholders
                echo '<script type="text/javascript">
                    jQuery(document).ready(function($) {
                        $("#bc-requests-toggle-admin-placeholders").on("click", function() {
                            $(".bc-requests-admin-placeholders").slideToggle();
                        });
                    });
                </script>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_admin_email_section'
        );

        // Add Customer Placeholders Section (collapsible)
        add_settings_field(
            'bc_space_requests_customer_placeholders',
            __('Available Placeholders', 'bc-space-requests'),
            function() {
                // Add collapsible section with placeholders
                echo '<div class="bc-requests-placeholders-toggle" style="margin-bottom: 10px;">';
                echo '<button type="button" class="button" id="bc-requests-toggle-customer-placeholders">' . __('Show/Hide Placeholders', 'bc-space-requests') . '</button>';
                echo '</div>';

                echo '<div class="bc-requests-customer-placeholders" style="display: none; background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin-top: 10px;">';
                echo '<p>' . __('You can use the following placeholders in your email subject and body:', 'bc-space-requests') . '</p>';
                echo '<ul style="list-style-type: disc; margin-left: 20px;">';

                // Load field labels from configuration file
                $field_labels = array();
                $labels_file = BC_REQUESTS_PLUGIN_DIR . 'includes/field-labels.php';

                if (file_exists($labels_file)) {
                    include $labels_file;
                    if (isset($bc_requests_field_labels) && is_array($bc_requests_field_labels)) {
                        $field_labels = $bc_requests_field_labels;
                    }
                }

                // Display available placeholders
                foreach ($field_labels as $field_key => $field_label) {
                    echo '<li><code>[' . esc_html($field_key) . ']</code> - ' . esc_html($field_label) . '</li>';
                }

                // Add special placeholders
                echo '<li><code>[request-id]</code> - ' . __('The ID of the request', 'bc-space-requests') . '</li>';
                echo '<li><code>[request-title]</code> - ' . __('The title of the request', 'bc-space-requests') . '</li>';
                echo '</ul>';
                echo '</div>';

                // Add JavaScript to toggle placeholders
                echo '<script type="text/javascript">
                    jQuery(document).ready(function($) {
                        $("#bc-requests-toggle-customer-placeholders").on("click", function() {
                            $(".bc-requests-customer-placeholders").slideToggle();
                        });
                    });
                </script>';
            },
            'bc-space-requests-settings',
            'bc_space_requests_customer_email_section'
        );
    }


    public function add_meta_boxes() {
        add_meta_box(
            'bc_request_status_meta_box',
            __('Request Status', 'bc-space-requests'),
            array($this, 'status_meta_box_callback'),
            'bc_request',
            'side',
            'high'
        );

        add_meta_box(
            'bc_request_calendar_assignments_meta_box',
            __('Assignacions al calendari', 'bc-space-requests'),
            array($this, 'calendar_assignments_meta_box_callback'),
            'bc_request',
            'side',
            'default'
        );

        add_meta_box(
            'bc_request_data_meta_box',
            __('Dades de la petició', 'bc-space-requests'),
            array($this, 'data_meta_box_callback'),
            'bc_request',
            'normal',
            'high'
        );

        // Nou meta box per arxius
        add_meta_box(
            'bc_request_files_meta_box',
            __('Documents adjunts', 'bc-space-requests'),
            array($this, 'files_meta_box_callback'),
            'bc_request',
            'normal',
            'high'
        );
    }

    /**
     * Mostra els arxius adjunts al formulari
     */
    public function files_meta_box_callback($post) {
        wp_nonce_field('bc_request_files_meta_box', 'bc_request_files_meta_box_nonce');

        $files = get_post_meta($post->ID, 'bc_request_files', true);

        if (empty($files)) {
            echo '<p>' . __('No hi ha documents adjunts.', 'bc-space-requests') . '</p>';
            return;
        }

        echo '<div class="bc-requests-files">';
        echo '<table class="widefat striped">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>' . __('File Name', 'bc-space-requests') . '</th>';
        echo '<th>' . __('Field', 'bc-space-requests') . '</th>';
        echo '<th>' . __('Type', 'bc-space-requests') . '</th>';
        echo '<th>' . __('Actions', 'bc-space-requests') . '</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($files as $file) {
            echo '<tr>';
            echo '<td>' . esc_html($file['original_name']) . '</td>';
            echo '<td>' . esc_html($file['field_name']) . '</td>';
            echo '<td>' . esc_html($file['mime_type']) . '</td>';
            echo '<td>';

            // Generate protected file URL instead of using direct URL
            $protected_file_url = home_url('?protected_file=' . $post->ID . '/' . basename($file['file_path']));

            echo '<a href="' . esc_url($protected_file_url) . '" target="_blank" class="button button-small">' . __('View', 'bc-space-requests') . '</a> ';
            echo '<a href="' . esc_url($protected_file_url) . '" download="' . esc_attr($file['original_name']) . '" class="button button-small">' . __('Download', 'bc-space-requests') . '</a>';
            echo '</td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        echo '<style>
            .bc-requests-files {
                margin-top: 10px;
            }
            .bc-requests-files .button {
                margin-right: 5px;
            }
        </style>';
    }

    /**
     * Afegeix el suport per afegir nous arxius manualment
     * Això es pot implementar en una versió futura
     */
    public function add_files_meta_box() {
        add_meta_box(
            'bc_request_add_files_meta_box',
            __('Afegir comentari', 'bc-space-requests'),
            array($this, 'add_files_meta_box_callback'),
            'bc_request',
            'normal',
            'default'
        );
    }


    /**
     * This method is no longer used as we're now using .htaccess files to protect the uploads directory
     * and the serve_protected_file method to serve files through a protected endpoint
     */
    public function protect_uploads_access() {
        // This functionality is now handled by .htaccess files and the serve_protected_file method
    }

    /**
     * Register protected files endpoint
     * This functionality is now handled by BC_Requests_Comment_Attachments class
     */


/*     public function register_protected_files_endpoint() {
        // This functionality is now handled by BC_Requests_Comment_Attachments::register_protected_file_endpoint
    }
*/

    public function add_files_meta_box_callback($post) {
        wp_nonce_field('bc_request_add_files_meta_box', 'bc_request_add_files_meta_box_nonce');

        // Get current user info
        $current_user = wp_get_current_user();

        echo '<div class="bc-request-comment-form" style="margin-bottom: 20px;">';

        // Add comment textarea
        echo '<p><label for="bc_request_comment"><strong>' . __('Text del comentari:', 'bc-space-requests') . '</strong></label></p>';
        echo '<textarea name="bc_request_comment" id="bc_request_comment" rows="4" style="width: 100%; margin-bottom: 10px;"></textarea>';

        // Add file upload field
        echo '<p><label for="bc_request_additional_files"><strong>' . __('Pujar arxius (opcional):', 'bc-space-requests') . '</strong></label></p>';
        echo '<input type="file" name="bc_request_additional_files[]" id="bc_request_additional_files" multiple style="margin-bottom: 10px;" />';
        echo '<p class="description">' . __('Seleccionar un o més arxius per adjuntar a aquest comentari.', 'bc-space-requests') . '</p>';

        // Add submit button
        echo '<p><input type="submit" name="bc_request_add_comment" class="button button-primary" value="' . __('Afegir comentari i arxius', 'bc-space-requests') . '" /></p>';

        echo '</div>';

        // Add script to ensure the form has the correct enctype
        echo '<script type="text/javascript">
            document.addEventListener("DOMContentLoaded", function() {
                var form = document.getElementById("post");
                if (form) {
                    form.enctype = "multipart/form-data";
                    console.log("BC Requests: Set form enctype to multipart/form-data");
                }
            });
        </script>';
    }

    /**
     * Process the comment and file upload from the Add Files meta box
     */
    public function process_add_files_meta_box($post_id) {
        // Check if this is a bc_request post
        if (get_post_type($post_id) !== 'bc_request') {
            return;
        }

        // Verify nonce
        if (!isset($_POST['bc_request_add_files_meta_box_nonce']) ||
            !wp_verify_nonce($_POST['bc_request_add_files_meta_box_nonce'], 'bc_request_add_files_meta_box')) {
            return;
        }

        // Check if user has permission
        if (!current_user_can('edit_post', $post_id) ||
            (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return;
        }

        // Check if the comment button was clicked
        if (!isset($_POST['bc_request_add_comment'])) {
            return;
        }

        // Get the comment text
        $comment_text = isset($_POST['bc_request_comment']) ? sanitize_textarea_field($_POST['bc_request_comment']) : '';

        // If no comment and no files, do nothing
        if (empty($comment_text) && (empty($_FILES['bc_request_additional_files']) || empty($_FILES['bc_request_additional_files']['name'][0]))) {
            return;
        }

        // Get current user
        $current_user = wp_get_current_user();

        // Create the comment
        $comment_data = array(
            'comment_post_ID' => $post_id,
            'comment_author' => $current_user->display_name,
            'comment_author_email' => $current_user->user_email,
            'comment_author_url' => $current_user->user_url,
            'comment_content' => $comment_text,
            'comment_type' => 'comment',
            'comment_parent' => 0,
            'user_id' => $current_user->ID,
            'comment_approved' => 1,
        );

        // Insert the comment
        $comment_id = wp_insert_comment($comment_data);

        if (!$comment_id) {
            return;
        }

        // Process file uploads if any
        if (!empty($_FILES['bc_request_additional_files']) && !empty($_FILES['bc_request_additional_files']['name'][0])) {
            // Setup upload directory
            $upload_dir = wp_upload_dir();
            $base_dir = $upload_dir['basedir'] . '/' . $this->uploads_dir;
            $request_dir = $base_dir . '/' . $post_id;

            // Create directory if it doesn't exist
            if (!file_exists($request_dir)) {
                wp_mkdir_p($request_dir);

                // Create index.php for security
                $index_file = $request_dir . '/index.php';
                if (!file_exists($index_file)) {
                    file_put_contents($index_file, '<?php // Silence is golden');
                }
            }

            $saved_files = array();
            $file_count = count($_FILES['bc_request_additional_files']['name']);

            for ($i = 0; $i < $file_count; $i++) {
                if ($_FILES['bc_request_additional_files']['error'][$i] !== UPLOAD_ERR_OK) {
                    continue;
                }

                // Get file info
                $file_name = sanitize_file_name($_FILES['bc_request_additional_files']['name'][$i]);
                $file_tmp = $_FILES['bc_request_additional_files']['tmp_name'][$i];
                $file_type = $_FILES['bc_request_additional_files']['type'][$i];
                $file_size = $_FILES['bc_request_additional_files']['size'][$i];
                $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);

                // Generate safe filename (current time + random string + extension)
                $safe_filename = time() . '-' . wp_generate_password(8, false) . '.' . $file_ext;

                // Destination path
                $destination = $request_dir . '/' . $safe_filename;

                // Move the file
                if (move_uploaded_file($file_tmp, $destination)) {
                    // Store file info
                    $saved_files[] = array(
                        'original_name' => $file_name,
                        'saved_name' => $safe_filename,
                        'file_path' => $destination,
                        'mime_type' => $file_type,
                        'file_size' => $file_size,
                        'request_id' => $post_id,
                        'comment_id' => $comment_id,
                        'user_id' => $current_user->ID,
                        'upload_time' => current_time('mysql')
                    );

                    error_log('BC Requests: File uploaded successfully: ' . $file_name);
                } else {
                    error_log('BC Requests: Failed to move uploaded file: ' . $file_tmp . ' to ' . $destination);
                }
            }

            // Save file info to comment meta
            if (!empty($saved_files)) {
                update_comment_meta($comment_id, 'bc_comment_files', $saved_files);
                error_log('BC Requests: Files attached to comment');
            }
        }

        // Redirect to avoid form resubmission
        wp_redirect(admin_url('post.php?post=' . $post_id . '&action=edit&message=1'));
        exit;
    }

    /**
     * Add file upload field to comment form
     */
    /**
     * Enqueue scripts for comment form
     */
    public function enqueue_comment_scripts() {
        global $post;

        // Only enqueue on bc_request post type
        if (!$post || $post->post_type !== 'bc_request') {
            return;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return;
        }

        // Register and enqueue the script
        wp_register_script(
            'bc-requests-comment-form',
            BC_REQUESTS_PLUGIN_URL . 'js/bc-requests-comment-form.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Localize the script with the nonce
        wp_localize_script(
            'bc-requests-comment-form',
            'bc_requests_comment_data',
            array(
                'nonce' => wp_create_nonce('bc_comment_file_upload')
            )
        );

        // Enqueue the script
        wp_enqueue_script('bc-requests-comment-form');
    }

    /**
     * Enqueue front-end scripts
     */
    public function enqueue_frontend_scripts() {
        // Register and enqueue the restrict_date.js script
        wp_register_script(
            'bc-requests-restrict-date',
            BC_REQUESTS_PLUGIN_URL . 'js/front/restrict_date.js',
            array(),
            '1.0.0',
            true
        );

        // Enqueue styles
        wp_enqueue_style('bc-requests-style', BC_REQUESTS_PLUGIN_URL . 'css/front/style.css');
        wp_enqueue_style('bc-requests-time-inputs', BC_REQUESTS_PLUGIN_URL . 'css/front/time-inputs.css');
        wp_enqueue_style('bc-requests-form-tabs', BC_REQUESTS_PLUGIN_URL . 'css/front/form-tabs.css');

        // Get the max date from settings
        $max_date = get_option($this->max_date_option_name);
        if (empty($max_date)) {
            // Default to 30 days from now
            $max_date = date('Y-m-d', strtotime('+30 days'));
        }

        // Localize the script with the max date
        wp_localize_script(
            'bc-requests-restrict-date',
            'bc_requests_date_data',
            array(
                'max_date' => $max_date
            )
        );

        // Enqueue the script on all pages
        wp_enqueue_script('bc-requests-restrict-date');

        // Enqueue the main front-end scripts
        wp_enqueue_script('front-scripts', BC_REQUESTS_PLUGIN_URL . 'js/front/scripts.js', array('jquery'), '1.0.0', true);

        // Register and enqueue the time-inputs.js script
        wp_register_script(
            'bc-requests-time-inputs',
            BC_REQUESTS_PLUGIN_URL . 'js/front/time-inputs.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Enqueue the time-inputs script
        wp_enqueue_script('bc-requests-time-inputs');

        // Register and enqueue the space-dropdowns.js script
        wp_register_script(
            'bc-requests-space-dropdowns',
            BC_REQUESTS_PLUGIN_URL . 'js/front/space-dropdowns.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Get available spaces
        $spaces = get_option($this->spaces_option_name);
        $spaces_array = !empty($spaces) ? explode(',', $spaces) : ['Teatre Zorrilla', 'Teatre Margarida Xirgu'];

        // Clean up spaces (trim whitespace)
        $spaces_array = array_map('trim', $spaces_array);

        // Localize the script with spaces
        wp_localize_script(
            'bc-requests-space-dropdowns',
            'bc_requests_spaces',
            $spaces_array
        );

        // Enqueue the space-dropdowns script
        wp_enqueue_script('bc-requests-space-dropdowns');

        // Register and enqueue the form-tabs.js script
        wp_register_script(
            'bc-requests-form-tabs',
            BC_REQUESTS_PLUGIN_URL . 'js/front/form-tabs.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Enqueue the form-tabs script
        wp_enqueue_script('bc-requests-form-tabs');

        // You can add conditional loading if needed
        // For example, only on specific pages or forms
        /*
        if (is_page('request-form') || has_shortcode(get_post()->post_content, 'contact-form-7')) {
            wp_enqueue_script('bc-requests-restrict-date');
            wp_enqueue_script('bc-requests-time-inputs');
        }
        */
    }

    /**
     * Modify the comment form defaults to add file upload field
     */
    public function modify_comment_form_defaults($defaults) {
        global $post;

        // Only modify for bc_request post type
        if (!$post || $post->post_type !== 'bc_request') {
            return $defaults;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return $defaults;
        }

        // Debug information
        error_log('BC Requests: Modifying comment form defaults for post ID: ' . $post->ID);

        // Add enctype attribute to the form
        $defaults['comment_form_before'] = '<form id="commentform" class="comment-form" enctype="multipart/form-data">';
        $defaults['comment_form_after'] = '</form>';
        $defaults['comment_form_open_override'] = true;

        // Add file upload field to the comment notes after
        $upload_field = '<div class="bc-comment-file-upload" style="margin-bottom: 15px; clear: both;">';
        $upload_field .= '<p><label for="bc_comment_file"><strong>' . __('Attach Files (optional)', 'bc-space-requests') . '</strong></label><br />';
        $upload_field .= '<input type="file" name="bc_comment_file[]" id="bc_comment_file" multiple />';
        $upload_field .= '<input type="hidden" name="bc_comment_file_nonce" value="' . wp_create_nonce('bc_comment_file_upload') . '" />';
        $upload_field .= '</p>';
        $upload_field .= '<p class="description">' . __('Select one or more files to attach to this comment.', 'bc-space-requests') . '</p>';
        $upload_field .= '</div>';

        $defaults['comment_notes_after'] = $upload_field . (isset($defaults['comment_notes_after']) ? $defaults['comment_notes_after'] : '');

        return $defaults;
    }

    /**
     * Include the comment form template
     */
    public function include_comment_form_template($post_id) {
        // Only include for bc_request post type
        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'bc_request') {
            return;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return;
        }

        // Debug information
        error_log('BC Requests: Including comment form template for post ID: ' . $post_id);

        // Include the template
        include_once BC_REQUESTS_PLUGIN_DIR . 'templates/comment-form-upload.php';
    }

    /**
     * Set the comment form enctype attribute
     */
    public function set_comment_form_enctype($defaults) {
        global $post;

        // Only modify for bc_request post type
        if (!$post || $post->post_type !== 'bc_request') {
            return $defaults;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return $defaults;
        }

        // Debug information
        error_log('BC Requests: Setting comment form enctype for post ID: ' . $post->ID);

        // Add enctype attribute to the form
        $defaults['comment_form_before'] = '<form id="commentform" class="comment-form" enctype="multipart/form-data">';
        $defaults['comment_form_after'] = '</form>';
        $defaults['comment_form_open_override'] = true;

        return $defaults;
    }

    /**
     * Add upload field after the comment textarea
     */
    public function add_upload_field_after_textarea($comment_field) {
        global $post;

        // Only add for bc_request post type
        if (!$post || $post->post_type !== 'bc_request') {
            return $comment_field;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return $comment_field;
        }

        // Debug information
        error_log('BC Requests: Adding upload field after textarea for post ID: ' . $post->ID);

        // Create the upload field HTML
        $upload_field = '<div class="bc-comment-file-upload" style="margin: 15px 0; padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">';
        $upload_field .= '<h4 style="margin: 0 0 10px 0; color: #333;">' . __('Attach Files (optional)', 'bc-space-requests') . '</h4>';
        $upload_field .= '<input type="file" name="bc_comment_file[]" id="bc_comment_file" multiple style="display: block; margin-bottom: 10px;" />';
        $upload_field .= '<input type="hidden" name="bc_comment_file_nonce" value="' . wp_create_nonce('bc_comment_file_upload') . '" />';
        $upload_field .= '<p class="description" style="margin: 5px 0 0 0; font-size: 0.9em; color: #666;">';
        $upload_field .= __('Select one or more files to attach to this comment.', 'bc-space-requests');
        $upload_field .= '</p>';
        $upload_field .= '</div>';

        // Add script to ensure the form has the correct enctype
        $upload_field .= '<script type="text/javascript">
            document.addEventListener("DOMContentLoaded", function() {
                var form = document.getElementById("commentform");
                if (form) {
                    form.enctype = "multipart/form-data";
                    console.log("BC Requests: Set form enctype to multipart/form-data");
                }
            });
        </script>';

        // Return the comment field followed by the upload field
        return $comment_field . $upload_field;
    }

    /**
     * Add file upload field to comment form
     */
    public function add_comment_file_upload_field($post_id) {
        // Only add to bc_request post type
        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'bc_request') {
            return;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            return;
        }

        // Debug information
        error_log('BC Requests: Adding file upload field to comment form for post ID: ' . $post_id);

        // Output the file upload field
        echo '<div class="bc-comment-file-upload" style="margin-bottom: 15px; clear: both;">';
        echo '<p><label for="bc_comment_file"><strong>' . __('Attach Files (optional)', 'bc-space-requests') . '</strong></label><br />';
        echo '<input type="file" name="bc_comment_file[]" id="bc_comment_file" multiple />';
        echo '<input type="hidden" name="bc_comment_file_nonce" value="' . wp_create_nonce('bc_comment_file_upload') . '" />';
        echo '</p>';
        echo '<p class="description">' . __('Select one or more files to attach to this comment.', 'bc-space-requests') . '</p>';
        echo '</div>';

        // Add script to ensure the form has the correct enctype
        echo '<script type="text/javascript">
            document.addEventListener("DOMContentLoaded", function() {
                var form = document.getElementById("commentform");
                if (form) {
                    form.enctype = "multipart/form-data";
                    console.log("BC Requests: Set form enctype to multipart/form-data");
                }
            });
        </script>';
    }

    /**
     * Handle file upload when comment is submitted
     */
    public function handle_comment_file_upload($comment_id, $comment_approved, $comment_data) {
        // Debug information
        error_log('BC Requests: Comment file upload handler called for comment ID: ' . $comment_id);

        // Check if this is a bc_request post
        $post = get_post($comment_data['comment_post_ID']);
        if (!$post || $post->post_type !== 'bc_request') {
            error_log('BC Requests: Not a bc_request post type');
            return;
        }

        // Check if user has permission to upload files
        if (!is_user_logged_in() || (!current_user_can('administrator') && !current_user_can('manage_bc_requests'))) {
            error_log('BC Requests: User does not have permission to upload files');
            return;
        }

        // Verify nonce
        if (!isset($_POST['bc_comment_file_nonce']) || !wp_verify_nonce($_POST['bc_comment_file_nonce'], 'bc_comment_file_upload')) {
            error_log('BC Requests: Invalid or missing nonce');
            return;
        }

        // Check if files were uploaded
        if (empty($_FILES['bc_comment_file']) || empty($_FILES['bc_comment_file']['name'][0])) {
            error_log('BC Requests: No files were uploaded');
            return;
        }

        error_log('BC Requests: File upload checks passed, proceeding with upload');

        // Setup upload directory
        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'] . '/' . $this->uploads_dir;
        $request_dir = $base_dir . '/' . $post->ID;

        // Create directory if it doesn't exist
        if (!file_exists($request_dir)) {
            wp_mkdir_p($request_dir);

            // Create index.php for security
            $index_file = $request_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, '<?php // Silence is golden');
            }
        }

        $saved_files = array();
        $file_count = count($_FILES['bc_comment_file']['name']);

        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['bc_comment_file']['error'][$i] !== UPLOAD_ERR_OK) {
                continue;
            }

            // Get file info
            $file_name = sanitize_file_name($_FILES['bc_comment_file']['name'][$i]);
            $file_tmp = $_FILES['bc_comment_file']['tmp_name'][$i];
            $file_type = $_FILES['bc_comment_file']['type'][$i];
            $file_size = $_FILES['bc_comment_file']['size'][$i];
            $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);

            // Generate safe filename (current time + random string + extension)
            $safe_filename = time() . '-' . wp_generate_password(8, false) . '.' . $file_ext;

            // Destination path
            $destination = $request_dir . '/' . $safe_filename;

            // Move the file
            if (move_uploaded_file($file_tmp, $destination)) {
                // Store file info
                $saved_files[] = array(
                    'original_name' => $file_name,
                    'saved_name' => $safe_filename,
                    'file_path' => $destination,
                    'mime_type' => $file_type,
                    'file_size' => $file_size,
                    'request_id' => $post->ID,
                    'comment_id' => $comment_id,
                    'user_id' => get_current_user_id(),
                    'upload_time' => current_time('mysql')
                );

                error_log('BC Requests: File uploaded successfully: ' . $file_name);
            } else {
                error_log('BC Requests: Failed to move uploaded file: ' . $file_tmp . ' to ' . $destination);
            }
        }

        // Save file info to comment meta
        if (!empty($saved_files)) {
            update_comment_meta($comment_id, 'bc_comment_files', $saved_files);
            error_log('BC Requests: Files attached to comment');
        }
    }

    /**
     * Display attached files in comments
     */
    public function display_comment_attachments($comment_text, $comment) {
        // Check if this is a bc_request post
        $post = get_post($comment->comment_post_ID);
        if (!$post || $post->post_type !== 'bc_request') {
            return $comment_text;
        }

        // Get attached files
        $files = get_comment_meta($comment->comment_ID, 'bc_comment_files', true);
        if (empty($files)) {
            return $comment_text;
        }

        // Clean up the comment text if it contains "Attached Files" section
        // This handles comments created before we fixed the duplicate issue

        /*
        $attached_files_text = "\n\n" . __('Arxius:', 'bc-space-requests');
        if (strpos($comment_text, $attached_files_text) !== false) {
            // Find the position of the "Attached Files" text
            $pos = strpos($comment_text, $attached_files_text);
            // Get only the part before "Attached Files"
            $comment_text = substr($comment_text, 0, $pos);
        }*/

        // Build file display
        $output = '<div class="bc-comment-files">';
        $output .= '<h4>' . __('Documents', 'bc-space-requests') . '</h4>';
        $output .= '<ul class="bc-comment-files-list">';

        foreach ($files as $file) {
            // Generate protected file URL
            $file_url = home_url('?protected_file=' . $post->ID . '/' . $file['saved_name']);

            $output .= '<li class="bc-comment-file">';
            $output .= '<span class="bc-comment-file-name">' . esc_html($file['original_name']) . '</span> ';

            // Display user who uploaded the file if available
            if (!empty($file['user_id'])) {
                $user = get_userdata($file['user_id']);
                if ($user) {
                    $output .= '<span class="bc-comment-file-uploader">(' . __('Uploaded by', 'bc-space-requests') . ': ' . esc_html($user->display_name) . ')</span> ';
                }
            }

            // Display upload time if available
            if (!empty($file['upload_time'])) {
                $output .= '<span class="bc-comment-file-time">(' . __('on', 'bc-space-requests') . ': ' . esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($file['upload_time']))) . ')</span>';
            }

            $output .= '<span class="bc-comment-file-actions">';
            $output .= '<a href="' . esc_url($file_url) . '" target="_blank" class="button button-small">' . __('View', 'bc-space-requests') . '</a> ';
            $output .= '<a href="' . esc_url($file_url) . '" download="' . esc_attr($file['original_name']) . '" class="button button-small">' . __('Download', 'bc-space-requests') . '</a>';
            $output .= '</span>';
            $output .= '</li>';
        }

        $output .= '</ul>';
        $output .= '</div>';

        // Add styles
        $output .= '<style>
            .bc-comment-files {
                margin-top: 15px;
                padding-top: 10px;
                border-top: 1px solid #eee;
            }
            .bc-comment-files h4 {
                margin-bottom: 10px;
            }
            .bc-comment-files-list {
                margin: 0;
                padding: 0;
                list-style: none;
            }
            .bc-comment-file {
                margin-bottom: 5px;
                padding: 5px;
                background: #f9f9f9;
                border-radius: 3px;
            }
            .bc-comment-file-name {
                font-weight: bold;
            }
            .bc-comment-file-uploader,
            .bc-comment-file-time {
                color: #666;
                font-size: 0.9em;
                font-style: italic;
                margin-left: 5px;
            }
            .bc-comment-file-actions {
                display: block;
                margin-top: 5px;
            }
            .bc-comment-file-actions .button {
                margin-right: 5px;
            }
        </style>';

        return $comment_text . $output;
    }

public function status_meta_box_callback($post) {
    wp_nonce_field('bc_request_status_meta_box', 'bc_request_status_meta_box_nonce');

    $value = get_post_meta($post->ID, 'bc_request_status', true);
    if (empty($value)) {
        $value = 'pending'; // Default value
    }
    ?>
    <select name="bc_request_status" id="bc_request_status" style="width: 100%;">
        <option value="pending" <?php selected($value, 'pending'); ?>><?php _e('Pendent', 'bc-space-requests'); ?></option>
        <option value="in-process" <?php selected($value, 'in-process'); ?>><?php _e('En Procés', 'bc-space-requests'); ?></option>
        <option value="resolved" <?php selected($value, 'resolved'); ?>><?php _e('Resolta', 'bc-space-requests'); ?></option>
    </select>
    <?php if (current_user_can('manage_options')): ?>
        <p class="description"><em>Current saved value: <code><?php echo esc_html($value); ?></code></em></p>
    <?php endif; ?>
    <?php
}

/**
 * Display calendar assignments for a request
 */
public function calendar_assignments_meta_box_callback($post) {
    // Get assignments for this request
    $assignments = BC_Requests_Calendar::get_request_assignments($post->ID);

    if (empty($assignments)) {
        echo '<p>' . __('No calendar assignments found for this request.', 'bc-space-requests') . '</p>';
        echo '<p><a href="' . admin_url('admin.php?page=bc-requests-calendar') . '" class="button button-secondary">' . __('Anar al calendari', 'bc-space-requests') . '</a></p>';
        return;
    }

    // Group assignments by space
    $assignments_by_space = [];
    foreach ($assignments as $assignment) {
        if (!isset($assignments_by_space[$assignment->space])) {
            $assignments_by_space[$assignment->space] = [];
        }
        $assignments_by_space[$assignment->space][] = $assignment;
    }

    echo '<div class="bc-request-calendar-assignments">';

    // Display assignments grouped by space
    foreach ($assignments_by_space as $space => $space_assignments) {
        // Get color for this space
        $color = BC_Requests_Calendar::get_color_for_space($space);

        echo '<div class="bc-request-space-assignments" style="margin-bottom: 15px;">';
        echo '<h4 style="margin: 0 0 5px 0; padding: 5px; background-color: ' . esc_attr($color) . '; color: #fff;">' . esc_html($space) . '</h4>';

        // Sort assignments by date
        usort($space_assignments, function($a, $b) {
            return strtotime($a->date) - strtotime($b->date);
        });

        echo '<ul style="margin: 0; padding: 0 0 0 20px;">';
        foreach ($space_assignments as $assignment) {
            // Format the date
            $date_obj = new DateTime($assignment->date);
            $formatted_date = $date_obj->format('d/m/Y');

            echo '<li>' . esc_html($formatted_date) . '</li>';
        }
        echo '</ul>';
        echo '</div>';
    }

    echo '<p style="margin-top: 10px;"><a href="' . admin_url('admin.php?page=bc-requests-calendar') . '" class="button button-secondary">' . __('Anar al calendari', 'bc-space-requests') . '</a></p>';

    echo '</div>';

    // Add some CSS
    echo '<style>
        .bc-request-calendar-assignments {
            margin-top: 10px;
        }
        .bc-request-space-assignments h4 {
            border-radius: 3px;
            font-size: 13px;
        }
        .bc-request-space-assignments ul {
            list-style-type: disc;
        }
    </style>';
}

    public function data_meta_box_callback($post) {
        wp_nonce_field('bc_request_data_meta_box', 'bc_request_data_meta_box_nonce');

        $data = get_post_meta($post->ID, 'bc_request_data', true);
        $data = maybe_unserialize($data);

        if (empty($data) || !is_array($data)) {
            echo '<p>' . __('No data available for this request.', 'bc-space-requests') . '</p>';
            return;
        }

        // Load field labels from configuration file
        $field_labels = [];
        $field_groups = [];
        $labels_file = BC_REQUESTS_PLUGIN_DIR . 'includes/field-labels.php';

        if (file_exists($labels_file)) {
            include $labels_file;
            if (isset($bc_requests_field_labels) && is_array($bc_requests_field_labels)) {
                $field_labels = $bc_requests_field_labels;
            }
            if (isset($bc_requests_field_groups) && is_array($bc_requests_field_groups)) {
                $field_groups = $bc_requests_field_groups;
            }
        }

        echo '<div class="bc-requests-form-data">';

        // If we have field groups, organize fields by groups
        if (!empty($field_groups)) {
            // Create tabs for each group
            echo '<div class="bc-requests-tabs">';
            echo '<ul class="bc-requests-tab-nav">';
            $first_tab = true;

            foreach ($field_groups as $group_name => $group_fields) {
                $group_id = sanitize_title($group_name);
                $active_class = $first_tab ? 'active' : '';
                echo '<li><a href="#' . esc_attr($group_id) . '" class="' . $active_class . '">' . esc_html($group_name) . '</a></li>';
                $first_tab = false;
            }

            // Add a tab for ungrouped fields

            /*
            echo '<li><a href="#bc-requests-ungrouped">'. __('Altres camps', 'bc-space-requests') .'</a></li>';
               */
            echo '</ul>';

            // Create content for each tab
            $first_tab = true;
            foreach ($field_groups as $group_name => $group_fields) {
                $group_id = sanitize_title($group_name);
                $display_style = $first_tab ? 'block' : 'none';

                echo '<div id="' . esc_attr($group_id) . '" class="bc-requests-tab-content" style="display: ' . $display_style . ';">';
                echo '<table class="form-table">';

                // Display fields in this group
                foreach ($group_fields as $field_key) {
                    // Get the value if it exists, otherwise use an empty string
                    $field_value = isset($data[$field_key]) ? $data[$field_key] : '';
                    $this->render_field_row($field_key, $field_value, $field_labels);
                }

                echo '</table>';
                echo '</div>';

                $first_tab = false;
            }


            // Create a tab for ungrouped fields
            echo '<div id="bc-requests-ungrouped" class="bc-requests-tab-content" style="display: none;">';
            echo '<table class="form-table">';

            // Find fields that aren't in any group
            $grouped_fields = [];
            foreach ($field_groups as $group_fields) {
                $grouped_fields = array_merge($grouped_fields, $group_fields);
            }

            // First, show all configured fields that aren't in any group
            foreach ($field_labels as $field_key => $field_label) {
                // Skip fields that are already in a group
                if (in_array($field_key, $grouped_fields)) {
                    continue;
                }

                // Get the value if it exists, otherwise use an empty string
                $field_value = isset($data[$field_key]) ? $data[$field_key] : '';
                $this->render_field_row($field_key, $field_value, $field_labels);
            }

            // Then show any fields in the data that aren't configured
            foreach ($data as $key => $value) {
                // Skip internal CF7 fields, already grouped fields, and fields that are in the field_labels
                if (strpos($key, '_wpcf7') === 0 || in_array($key, $grouped_fields) || isset($field_labels[$key])) {
                    continue;
                }

                $this->render_field_row($key, $value, $field_labels);
            }

            echo '</table>';
            echo '</div>';

            echo '</div>'; // Close tabs container

            // Add JavaScript for tabs
            echo '<script type="text/javascript">
                jQuery(document).ready(function($) {
                    $(".bc-requests-tab-nav a").on("click", function(e) {
                        e.preventDefault();

                        // Hide all tab contents
                        $(".bc-requests-tab-content").hide();

                        // Remove active class from all tabs
                        $(".bc-requests-tab-nav a").removeClass("active");

                        // Show the selected tab content
                        $($(this).attr("href")).show();

                        // Add active class to the clicked tab
                        $(this).addClass("active");
                    });
                });
            </script>';

            // Add CSS for tabs and empty fields
            echo '<style>
                /* Tabs styling */
                .bc-requests-tab-nav {
                    display: flex;
                    list-style: none;
                    padding: 0;
                    margin: 0 0 20px 0;
                    border-bottom: 1px solid #ccc;
                }
                .bc-requests-tab-nav li {
                    margin: 0;
                }
                .bc-requests-tab-nav a {
                    display: block;
                    padding: 10px 15px;
                    text-decoration: none;
                    color: #555;
                    font-weight: 500;
                    border: 1px solid transparent;
                    border-bottom: none;
                    margin-bottom: -1px;
                }
                .bc-requests-tab-nav a.active {
                    border-color: #ccc;
                    border-bottom-color: #f1f1f1;
                    background: #f1f1f1;
                }
                .bc-requests-tab-nav a:hover:not(.active) {
                    background: #f9f9f9;
                }
                .bc-requests-tab-content {
                    background: #f1f1f1;
                    padding: 15px;
                    border: 1px solid #ccc;
                    border-top: none;
                }

                /* Empty fields styling */
                .bc-empty-field th {
                    color: #888;
                }
                .bc-empty-indicator {
                    color: #999;
                    font-size: 0.9em;
                    font-style: italic;
                }
                .bc-empty-field input,
                .bc-empty-field textarea {
                    background-color: #f9f9f9;
                    border-color: #ddd;
                }
                .bc-empty-field input::placeholder,
                .bc-empty-field textarea::placeholder {
                    color: #bbb;
                }
            </style>';
        } else {
            // No groups, just display all fields in a table
            echo '<table class="form-table">';

            // First, show all configured fields
            foreach ($field_labels as $field_key => $field_label) {
                // Get the value if it exists, otherwise use an empty string
                $field_value = isset($data[$field_key]) ? $data[$field_key] : '';
                $this->render_field_row($field_key, $field_value, $field_labels);
            }

            // Then show any fields in the data that aren't configured
            foreach ($data as $key => $value) {
                // Skip internal CF7 fields and fields that are in the field_labels
                if (strpos($key, '_wpcf7') === 0 || isset($field_labels[$key])) {
                    continue;
                }

                $this->render_field_row($key, $value, $field_labels);
            }

            echo '</table>';
        }

        echo '</div>'; // Close form data container
    }

    /**
     * Render a single field row
     */
    private function render_field_row($key, $value, $field_labels) {
        // Check if the field is empty
        $is_empty = (is_array($value) && empty($value)) || (!is_array($value) && trim($value) === '');

        // Skip rendering empty fields entirely
        if ($is_empty) {
            return;
        }

        $field_id = 'bc_request_' . sanitize_key($key);
        $field_name = 'bc_request_data[' . esc_attr($key) . ']';

        // Get human-readable label from config or use the key
        $label = isset($field_labels[$key]) ? $field_labels[$key] : $key;

        echo '<tr>';
        echo '<th scope="row">';
        echo '<label for="' . esc_attr($field_id) . '">' . esc_html($label) . '</label>';
        echo '</th>';
        echo '<td>';

        // Define fields that should always be rendered as textareas
        $textarea_fields = [
            'descripcio-acte',
            'activitat-descripcio',
            'comentaris-tecnic',
            'necessitats-tecniques',
            'material-tecnic',
            'observacions',
            'comentaris'
        ];

        // Define fields that should have the "admin" class
        $admin_fields = [
            'comentaris-tecnic',
            //'necessitats-tecniques',
           // 'observacions',
           // 'comentaris'
        ];

        if (is_array($value)) {
            // Handle array values
            $admin_class = in_array($key, $admin_fields) ? ' admin' : '';
            echo '<textarea name="' . esc_attr($field_name) . '" id="' . esc_attr($field_id) . '" class="large-text' . $admin_class . '" rows="4">'
                . esc_textarea(!empty($value) ? implode(', ', $value) : '') . '</textarea>';
        } else {
            // Check if this field should always be a textarea
            if (in_array($key, $textarea_fields)) {
                $admin_class = in_array($key, $admin_fields) ? ' admin' : '';
                echo '<textarea name="' . esc_attr($field_name) . '" id="' . esc_attr($field_id) . '" class="large-text' . $admin_class . '" rows="4">'
                    . esc_textarea($value) . '</textarea>';
            }
            // Determine field type based on content length
            else if (strlen($value) > 50 || strpos($value, "\n") !== false) {
                $admin_class = in_array($key, $admin_fields) ? ' admin' : '';
                echo '<textarea name="' . esc_attr($field_name) . '" id="' . esc_attr($field_id) . '" class="large-text' . $admin_class . '" rows="3">'
                    . esc_textarea($value) . '</textarea>';
            } else {
                echo '<input type="text" name="' . esc_attr($field_name) . '" id="' . esc_attr($field_id) . '" value="'
                    . esc_attr($value) . '" class="regular-text">';
            }
        }

        // Show the original field key for reference (only for admins)
        if (current_user_can('manage_options')) {
            echo '<p class="description"><small><em>Field ID: ' . esc_html($key) . '</em></small></p>';
        }

        echo '</td>';
        echo '</tr>';
    }

       public function save_meta_data($post_id) {
        // Prevent infinite loop
        static $is_saving = false;
        if ($is_saving) {
            return;
        }

        try {
            $is_saving = true;

            // Check if this is an autosave
            if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
                $is_saving = false;
                return;
            }

            // Check permissions
            if (!current_user_can('edit_bc_request', $post_id) && !current_user_can(BC_Requests_Roles::CAPABILITY)) {
                $is_saving = false;
                return;
            }

            // Save the status field
            if (isset($_POST['bc_request_status'])) {
                // Verify the status meta box nonce
                if (isset($_POST['bc_request_status_meta_box_nonce']) &&
                    wp_verify_nonce($_POST['bc_request_status_meta_box_nonce'], 'bc_request_status_meta_box')) {

                    $status = sanitize_text_field($_POST['bc_request_status']);
                    update_post_meta($post_id, 'bc_request_status', $status);

                    // Log status update for debugging
                    error_log('BC Requests: Status updated for request #' . $post_id . ' to: ' . $status);
                }
            }

            // Save the form data
            if (isset($_POST['bc_request_data']) && is_array($_POST['bc_request_data'])) {
                // Verify the data meta box nonce
                if (isset($_POST['bc_request_data_meta_box_nonce']) &&
                    wp_verify_nonce($_POST['bc_request_data_meta_box_nonce'], 'bc_request_data_meta_box')) {

                    $old_data = get_post_meta($post_id, 'bc_request_data', true);
                    $old_data = maybe_unserialize($old_data);

                    if (!is_array($old_data)) {
                        $old_data = array();
                    }

                    $new_data = array();
                    foreach ($_POST['bc_request_data'] as $key => $value) {
                        $key = sanitize_key($key);
                        if (is_array($value)) {
                            $new_data[$key] = array_map('sanitize_text_field', $value);
                        } else {
                            $new_data[$key] = sanitize_text_field($value);
                        }
                    }

                    // Preserve internal CF7 fields
                    foreach ($old_data as $key => $value) {
                        if (strpos($key, '_wpcf7') === 0) {
                            $new_data[$key] = $value;
                        }
                    }

                    // Update meta data first
                    update_post_meta($post_id, 'bc_request_data', $new_data);

                    // Then update post content
                    remove_action('save_post', array($this, 'save_meta_data'));

                    // Load field labels from configuration file
                    $field_labels = [];
                    $labels_file = BC_REQUESTS_PLUGIN_DIR . 'includes/field-labels.php';

                    if (file_exists($labels_file)) {
                        include $labels_file;
                        if (isset($bc_requests_field_labels) && is_array($bc_requests_field_labels)) {
                            $field_labels = $bc_requests_field_labels;
                        }
                    }

                    $content = '';
                    foreach ($new_data as $key => $value) {
                        if (strpos($key, '_wpcf7') !== 0) {
                            if (is_array($value)) {
                                $value = implode(', ', $value);
                            }

                            // Get human-readable label from config or use the key
                            $label = isset($field_labels[$key]) ? $field_labels[$key] : $key;

                            $content .= '<strong>' . esc_html($label) . ':</strong> ' . esc_html($value) . '<br>';
                        }
                    }

                    wp_update_post(array(
                        'ID' => $post_id,
                        'post_content' => $content
                    ));

                    add_action('save_post', array($this, 'save_meta_data'));
                }
            }
        } catch (Exception $e) {
            error_log('BC Requests save_meta_data error: ' . $e->getMessage());
        } finally {
            $is_saving = false;
        }
    }

    public function set_custom_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['request_id'] = __('ID', 'bc-space-requests');
        $new_columns['title'] = $columns['title'];
        $new_columns['status'] = __('Status', 'bc-space-requests');
        $new_columns['date'] = $columns['date'];

        return $new_columns;
    }

    public function custom_column_content($column, $post_id) {
        if ($column == 'request_id') {
            echo '<span class="request-id">' . esc_html($post_id) . '</span>';
        } elseif ($column == 'status') {
            $status = get_post_meta($post_id, 'bc_request_status', true);
            if (empty($status)) {
                $status = 'pending'; // Default status
            }

            // Load status icons
            include_once BC_REQUESTS_PLUGIN_DIR . 'includes/status-icons.php';

            // Make sure the variable is available
            global $bc_requests_status_icons;

            // If the icons array is not defined or the status is not in the array, use default
            if (!isset($bc_requests_status_icons) || !is_array($bc_requests_status_icons)) {
                // Define default icons if the file didn't load properly
                $bc_requests_status_icons = [
                    'pending'    => 'dashicons-ellipsis',
                    'in-process' => 'dashicons-clock',
                    'resolved'   => 'dashicons-yes',
                    'default'    => 'dashicons-warning'
                ];
            }

            $status_icon = isset($bc_requests_status_icons[$status]) ? $bc_requests_status_icons[$status] : $bc_requests_status_icons['default'];

            switch ($status) {
                case 'pending':
                    echo '<span class="bc-request-status-icon"><span class="dashicons ' . esc_attr($status_icon) . '"></span> ' . __('Pendent', 'bc-space-requests') . '</span>';
                    break;
                case 'in-process':
                    echo '<span class="bc-request-status-icon"><span class="dashicons ' . esc_attr($status_icon) . '"></span> ' . __('En Procés', 'bc-space-requests') . '</span>';
                    break;
                case 'resolved':
                    echo '<span class="bc-request-status-icon"><span class="dashicons ' . esc_attr($status_icon) . '"></span> ' . __('Resolta', 'bc-space-requests') . '</span>';
                    break;
                default:
                    echo '<span class="bc-request-status-icon"><span class="dashicons ' . esc_attr($status_icon) . '"></span> ' . __('Unknown', 'bc-space-requests') . '</span>';
            }
        }
    }

    public function add_status_filter() {
        global $typenow;

        if ($typenow == 'bc_request') {
            $current = isset($_GET['bc_request_status']) ? $_GET['bc_request_status'] : '';
            ?>
            <select name="bc_request_status" id="bc_request_status">
                <option value=""><?php _e('Totes', 'bc-space-requests'); ?></option>
                <option value="pending" <?php selected($current, 'pending'); ?>><?php _e('Pendent', 'bc-space-requests'); ?></option>
                <option value="in-process" <?php selected($current, 'in-process'); ?>><?php _e('En Procés', 'bc-space-requests'); ?></option>
                <option value="resolved" <?php selected($current, 'resolved'); ?>><?php _e('Resolta', 'bc-space-requests'); ?></option>
            </select>
            <?php
        }
    }

    public function filter_requests_by_status($query) {
        global $pagenow;

        if (is_admin() && $pagenow == 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] == 'bc_request' && isset($_GET['bc_request_status']) && $_GET['bc_request_status'] != '') {
            $query->query_vars['meta_key'] = 'bc_request_status';
            $query->query_vars['meta_value'] = sanitize_text_field($_GET['bc_request_status']);
        }
    }

    /**
     * Enqueue admin styles and scripts
     */
    /**
     * Customize the email components for Contact Form 7
     *
     * @param array $components The email components
     * @param object $contact_form The contact form object
     * @param object $mail The mail object
     * @return array The modified email components
     */
    public function customize_mail_components($components, $contact_form, $mail) {
        // Get the form ID
        $form_id = $contact_form->id();

        // Debug logging
        error_log('[BC Requests] Customizing mail components. Form ID: ' . $form_id);

        // Check if this is our target form
        $target_form_id = get_option($this->option_name);
        error_log('[BC Requests] Target form ID from settings: ' . $target_form_id);

        // Convert both IDs to integers for comparison to handle different formats
        $form_id_int = intval($form_id);
        $target_form_id_int = intval($target_form_id);

        error_log('[BC Requests] Comparing form IDs (int): ' . $form_id_int . ' vs ' . $target_form_id_int);

        if ($form_id != $target_form_id && $form_id_int != $target_form_id_int) {
            error_log('[BC Requests] Form ID mismatch - not customizing email');
            return $components;
        }

        error_log('[BC Requests] Form ID match - customizing email');

        // Get the latest request ID
        global $wpdb;
        $latest_request = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts}
                WHERE post_type = %s
                ORDER BY ID DESC
                LIMIT 1",
                'bc_request'
            )
        );

        // Get the submission data
        $submission = WPCF7_Submission::get_instance();
        if (!$submission) {
            return $components;
        }

        $posted_data = $submission->get_posted_data();

        // Check if this is the mail to admin (mail_1) or to customer (mail_2)
        $is_admin_mail = true;
        $mail_index = 1;

        if (isset($mail->name)) {
            error_log('[BC Requests] Mail name: ' . $mail->name);
            if ($mail->name == 'mail_2') {
                $is_admin_mail = false;
                $mail_index = 2;
                error_log('[BC Requests] Processing customer email (mail_2)');
            } else {
                error_log('[BC Requests] Processing admin email (mail_1)');
            }
        } else {
            // If mail->name is not set, try to determine from the recipient
            if (isset($components['recipient']) && strpos($components['recipient'], '[your-email]') !== false) {
                $is_admin_mail = false;
                $mail_index = 2;
                error_log('[BC Requests] Detected customer email based on recipient containing [your-email]');
            }
        }

        // Get the appropriate email subject and body from settings
        if ($is_admin_mail) {
            // Admin email
            $subject = get_option($this->email_subject_option_name);
            $body = get_option($this->email_body_option_name);

            // If no custom settings, use the default ones
            if (empty($subject)) {
                $subject = __('New Space Request: [espai-1] - Request #[request-id]', 'bc-space-requests');
            }

            if (empty($body)) {
                $body = __("A new space request has been submitted.\n\nRequest Details:\nID: [request-id]\nRequester: [name]\nEmail: [your-email]\nPhone: [telefon]\nEntity: [entitat]\nNIF/CIF: [nif]\nSpace: [espai-1]\nDate: [data-acte]\n\nView and manage this request here: [link-request]", 'bc-space-requests');
            }

            // Get the custom admin email
            $admin_email = get_option($this->admin_email_option_name);
            if (empty($admin_email)) {
                $admin_email = get_option('admin_email'); // Use the site admin email as default
            }

            // Update the recipient for admin email
            $components['recipient'] = $admin_email;

            error_log('[BC Requests] Set admin email recipient to: ' . $admin_email);
        } else {
            // Customer email
            $subject = get_option($this->customer_email_subject_option_name);
            $body = get_option($this->customer_email_body_option_name);

            // If no custom settings, use the default ones
            if (empty($subject)) {
                $subject = __('Your space request has been received - Request #[request-id]', 'bc-space-requests');
            }

            if (empty($body)) {
                $body = __("Dear [name],\n\nThank you for your space request. We have received your request and it is being processed.\n\nRequest Details:\nRequest ID: [request-id]\nSpace: [espai-1]\nDate: [data-acte]\n\nWe will contact you soon regarding your request.\n\nBest regards,\nBadalona Cultura", 'bc-space-requests');
            }

            // Make sure the customer email goes to the customer
            // Don't modify the recipient for customer email - it should already be set to [your-email]
            error_log('[BC Requests] Customer email recipient: ' . $components['recipient']);
        }

        // Replace placeholders in subject and body
        foreach ($posted_data as $key => $value) {
            if (is_array($value)) {
                $value = implode(', ', $value);
            }

            $subject = str_replace('[' . $key . ']', $value, $subject);
            $body = str_replace('[' . $key . ']', $value, $body);
        }

        // Replace special placeholders
        $request_id = $latest_request ? $latest_request : time();
        $subject = str_replace('[request-id]', $request_id, $subject);
        $body = str_replace('[request-id]', $request_id, $body);

        // Get the request title if available
        $request_title = '';
        if ($request_id) {
            $request = get_post($request_id);
            if ($request) {
                $request_title = $request->post_title;
                $subject = str_replace('[request-title]', $request_title, $subject);
                $body = str_replace('[request-title]', $request_title, $body);
            }
        }

        // Create the request link (only for admin email)
        if ($is_admin_mail) {
            $request_link = admin_url('post.php?post=' . $request_id . '&action=edit');
            $subject = str_replace('[link-request]', $request_link, $subject);
            $body = str_replace('[link-request]', $request_link, $body);
        }

        // Update the components
        $components['subject'] = $subject;
        $components['body'] = $body;

        error_log('[BC Requests] Final email components for mail_' . $mail_index . ': Subject: ' . $subject . ', Recipient: ' . $components['recipient']);

        return $components;
    }

    /**
     * Determine whether to skip the default Contact Form 7 mail sending
     *
     * @param bool $skip Whether to skip sending mail
     * @param object $contact_form The contact form object
     * @return bool Whether to skip sending mail
     */
    public function maybe_skip_cf7_mail($skip, $contact_form) {
        // Get the form ID
        $form_id = $contact_form->id();
        $target_form_id = get_option($this->option_name);

        // Convert both IDs to integers for comparison to handle different formats
        $form_id_int = intval($form_id);
        $target_form_id_int = intval($target_form_id);

        // Only skip mail for our target form
        if ($form_id == $target_form_id || $form_id_int == $target_form_id_int) {
            error_log('[BC Requests] Skipping default CF7 mail for form ID: ' . $form_id);

            // Get the submission data
            $submission = WPCF7_Submission::get_instance();
            if ($submission) {
                $posted_data = $submission->get_posted_data();

                // Send our custom emails
                $this->send_custom_emails($contact_form, $posted_data);
            }

            // Skip the default mail
            return true;
        }

        // Don't skip mail for other forms
        return $skip;
    }

    /**
     * Send custom emails for the form submission
     *
     * @param object $contact_form The contact form object
     * @param array $posted_data The form data
     */
    private function send_custom_emails($contact_form, $posted_data) {
        // Get the latest request ID
        global $wpdb;
        $latest_request = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts}
                WHERE post_type = %s
                ORDER BY ID DESC
                LIMIT 1",
                'bc_request'
            )
        );

        $request_id = $latest_request ? $latest_request : time();

        // 1. Send admin email
        $admin_subject = get_option($this->email_subject_option_name);
        $admin_body = get_option($this->email_body_option_name);

        // If no custom settings, use the default ones
        if (empty($admin_subject)) {
            $admin_subject = __('New Space Request: [espai-1] - Request #[request-id]', 'bc-space-requests');
        }

        if (empty($admin_body)) {
            $admin_body = __("A new space request has been submitted.\n\nRequest Details:\nID: [request-id]\nRequester: [name]\nEmail: [your-email]\nPhone: [telefon]\nEntity: [entitat]\nNIF/CIF: [nif]\nSpace: [espai-1]\nDate: [data-acte]\n\nView and manage this request here: [link-request]", 'bc-space-requests');
        }

        // Replace placeholders in subject and body
        foreach ($posted_data as $key => $value) {
            if (is_array($value)) {
                $value = implode(', ', $value);
            }

            $admin_subject = str_replace('[' . $key . ']', $value, $admin_subject);
            $admin_body = str_replace('[' . $key . ']', $value, $admin_body);
        }

        // Replace special placeholders
        $admin_subject = str_replace('[request-id]', $request_id, $admin_subject);
        $admin_body = str_replace('[request-id]', $request_id, $admin_body);

        // Get the request title if available
        $request_title = '';
        if ($request_id) {
            $request = get_post($request_id);
            if ($request) {
                $request_title = $request->post_title;
                $admin_subject = str_replace('[request-title]', $request_title, $admin_subject);
                $admin_body = str_replace('[request-title]', $request_title, $admin_body);
            }
        }

        // Create the request link
        $request_link = admin_url('post.php?post=' . $request_id . '&action=edit');
        $admin_subject = str_replace('[link-request]', $request_link, $admin_subject);
        $admin_body = str_replace('[link-request]', $request_link, $admin_body);

        // Get the custom admin email
        $admin_email = get_option($this->admin_email_option_name);
        if (empty($admin_email)) {
            $admin_email = get_option('admin_email'); // Use the site admin email as default
        }

        // Send admin email
        $admin_headers = array('Content-Type: text/html; charset=UTF-8');
        wp_mail($admin_email, $admin_subject, nl2br($admin_body), $admin_headers);
        error_log('[BC Requests] Sent custom admin email to: ' . $admin_email);

        // 2. Send customer email
        if (isset($posted_data['your-email']) && !empty($posted_data['your-email'])) {
            $customer_email = $posted_data['your-email'];

            $customer_subject = get_option($this->customer_email_subject_option_name);
            $customer_body = get_option($this->customer_email_body_option_name);

            // If no custom settings, use the default ones
            if (empty($customer_subject)) {
                $customer_subject = __('Your space request has been received - Request #[request-id]', 'bc-space-requests');
            }

            if (empty($customer_body)) {
                $customer_body = __("Dear [name],\n\nThank you for your space request. We have received your request and it is being processed.\n\nRequest Details:\nRequest ID: [request-id]\nSpace: [espai-1]\nDate: [data-acte]\n\nWe will contact you soon regarding your request.\n\nBest regards,\nBadalona Cultura", 'bc-space-requests');
            }

            // Replace placeholders in subject and body
            foreach ($posted_data as $key => $value) {
                if (is_array($value)) {
                    $value = implode(', ', $value);
                }

                $customer_subject = str_replace('[' . $key . ']', $value, $customer_subject);
                $customer_body = str_replace('[' . $key . ']', $value, $customer_body);
            }

            // Replace special placeholders
            $customer_subject = str_replace('[request-id]', $request_id, $customer_subject);
            $customer_body = str_replace('[request-id]', $request_id, $customer_body);

            // Get the request title if available
            if ($request_id) {
                $request = get_post($request_id);
                if ($request) {
                    $request_title = $request->post_title;
                    $customer_subject = str_replace('[request-title]', $request_title, $customer_subject);
                    $customer_body = str_replace('[request-title]', $request_title, $customer_body);
                }
            }

            // Send customer email
            $customer_headers = array('Content-Type: text/html; charset=UTF-8');
            wp_mail($customer_email, $customer_subject, nl2br($customer_body), $customer_headers);
            error_log('[BC Requests] Sent custom customer email to: ' . $customer_email);
        } else {
            error_log('[BC Requests] No customer email found in form data');
        }
    }

    public function admin_enqueue_styles($hook) {
        global $post, $typenow;

        // For the bc_request post type list screen
        if ($hook == 'edit.php' && $typenow === 'bc_request') {
            // Enqueue the CSS for showing request ID in list view
            wp_enqueue_style(
                'bc-requests-show-id',
                BC_REQUESTS_PLUGIN_URL . 'css/admin/show-request-id.css',
                array(),
                '1.0.0'
            );

            // Enqueue the CSS for status icons
            wp_enqueue_style(
                'bc-requests-status-icons',
                BC_REQUESTS_PLUGIN_URL . 'css/admin/status-icons.css',
                array(),
                '1.0.0'
            );

            // Enqueue dashicons if not already loaded
            wp_enqueue_style('dashicons');
        }

        // For the bc_request post type edit screen
        if (($hook == 'post.php' || $hook == 'post-new.php') &&
            isset($post) && $post->post_type === 'bc_request') {

            // Enqueue the CSS to hide the "Add Comment" button
            wp_enqueue_style(
                'bc-requests-hide-add-comment',
                BC_REQUESTS_PLUGIN_URL . 'css/admin/hide-add-comment.css',
                array(),
                '1.0.0'
            );

            // Enqueue the CSS for showing request ID in edit view
            wp_enqueue_style(
                'bc-requests-show-id',
                BC_REQUESTS_PLUGIN_URL . 'css/admin/show-request-id.css',
                array(),
                '1.0.0'
            );

            // Enqueue the CSS for admin textareas
            wp_enqueue_style(
                'bc-requests-admin-textarea',
                BC_REQUESTS_PLUGIN_URL . 'css/admin/admin-textarea.css',
                array(),
                '1.0.0'
            );

            // Enqueue the JavaScript for showing request ID in edit view
            wp_enqueue_script(
                'bc-requests-show-id',
                BC_REQUESTS_PLUGIN_URL . 'js/admin/show-request-id.js',
                array('jquery'),
                '1.0.0',
                true
            );
        }
    }

   public function redirect_after_submission($contact_form) {
       // Check if we're on the reserva-espais page template
        if (is_page_template('page-reserva-espais.php')) {
            // Skip the mail sending process
            $submission = WPCF7_Submission::get_instance();
            if ($submission) {
                $submission->set_status('mail_sent'); // Set status as sent even without mail
            }
            
            // Redirect immediately
            wp_redirect('https://www.badalonacultura.cat/confirmacio-reserva');
            exit;
        }
    }

}

// Initialize the plugin
$bc_space_requests = new BC_Space_Requests();

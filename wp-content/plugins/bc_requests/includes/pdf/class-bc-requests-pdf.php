<?php
/**
 * BC Requests PDF Generator
 *
 * Handles PDF generation for BC Requests
 *
 * @package BC_Requests
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BC_Requests_PDF Class
 */
class BC_Requests_PDF {

    /**
     * Initialize the class
     */
    public static function init() {
        // Add AJAX endpoint for PDF generation
        add_action('wp_ajax_bc_requests_generate_pdf', array(__CLASS__, 'generate_pdf'));
        add_action('wp_ajax_nopriv_bc_requests_generate_pdf', array(__CLASS__, 'generate_pdf'));

        // Add metabox to the BC Request interface
        add_action('add_meta_boxes', array(__CLASS__, 'add_pdf_metabox'));

        // Enqueue scripts and styles
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts($hook) {
        global $post;

        // Only enqueue on bc_request post type edit screen
        if (($hook == 'post.php' || $hook == 'post-new.php') &&
            isset($post) && $post->post_type === 'bc_request') {

            // Get plugin version or use a default
            $version = defined('BC_REQUESTS_VERSION') ? BC_REQUESTS_VERSION : '1.0.0';

            // Enqueue the CSS for the PDF button
            wp_enqueue_style(
                'bc-requests-pdf-button',
                BC_REQUESTS_PLUGIN_URL . 'css/admin/pdf-button.css',
                array(),
                $version
            );

            // Enqueue the JavaScript for the PDF button
            wp_enqueue_script(
                'bc-requests-pdf-button',
                BC_REQUESTS_PLUGIN_URL . 'js/admin/pdf-button.js',
                array('jquery'),
                $version,
                true
            );

            // Localize the script with the AJAX URL
            wp_localize_script(
                'bc-requests-pdf-button',
                'bc_requests_pdf',
                array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('bc_requests_pdf_nonce'),
                    'post_id' => $post->ID,
                    'loading_text' => __('Generant...', 'bc-space-requests'),
                    'button_text' => __('Descarregar Sol·licitud', 'bc-space-requests')
                )
            );
        }
    }

    /**
     * Add PDF metabox to the BC Request interface
     */
    public static function add_pdf_metabox() {
        add_meta_box(
            'bc_requests_pdf_metabox',
            __('Descarregar Sol·licitud', 'bc-space-requests'),
            array(__CLASS__, 'render_pdf_metabox'),
            'bc_request',
            'side',
            'high'
        );
    }

    /**
     * Render PDF metabox content
     */
    public static function render_pdf_metabox($post) {
        echo '<div class="bc-requests-pdf-button-container">';
        echo '<p>' . __('Descarrega tota la informació de la sol·licitud, incloent-hi assignacions de calendari, camps i comentaris.', 'bc-space-requests') . '</p>';
        echo '<button type="button" id="bc-requests-pdf-button" class="button button-primary" style="width: 100%;">';
        echo '<span class="dashicons dashicons-media-document"></span> ';
        echo __('Descarregar Sol·licitud', 'bc-space-requests');
        echo '</button>';
        echo '<p class="description" style="margin-top: 8px; font-size: 12px;">' . __('Es descarrega com a HTML que es pot imprimir a PDF.', 'bc-space-requests') . '</p>';
        echo '</div>';
    }

    /**
     * Generate PDF for a BC Request
     */
    public static function generate_pdf() {
        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'bc_requests_pdf_nonce')) {
            wp_send_json_error(array('message' => __('Invalid security token.', 'bc-space-requests')));
            exit;
        }

        // Check if post ID is provided
        if (!isset($_REQUEST['post_id'])) {
            wp_send_json_error(array('message' => __('No request ID provided.', 'bc-space-requests')));
            exit;
        }

        $post_id = intval($_REQUEST['post_id']);

        // Check if post exists and is a bc_request
        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'bc_request') {
            wp_send_json_error(array('message' => __('Invalid request ID.', 'bc-space-requests')));
            exit;
        }

        // Check if user has permission to view this post
        if (!current_user_can('read_bc_request', $post_id) && !current_user_can('manage_bc_requests')) {
            wp_send_json_error(array('message' => __('You do not have permission to view this request.', 'bc-space-requests')));
            exit;
        }

        // Load our mPDF helper class
        require_once BC_REQUESTS_PLUGIN_DIR . 'includes/pdf/class-bc-requests-mpdf.php';

        // Get request data as HTML
        $html_content = self::get_request_data($post);

        // Set filename for download
        $filename = 'bc-request-' . $post_id . '.pdf';

        // Generate and output the PDF
        BC_Requests_MPDF::generate_pdf($html_content, $filename, 'D');
        exit;
    }

    /**
     * Create PDF document for a BC Request
     *
     * @deprecated This method is no longer used. Use get_request_data() and BC_Requests_MPDF::generate_pdf() instead.
     */
    private static function create_pdf_document($post) {
        // This method is kept for backward compatibility
        return null;
    }

    /**
     * Get request data formatted as HTML for PDF
     */
    private static function get_request_data($post) {
        // Get request data
        $data = get_post_meta($post->ID, 'bc_request_data', true);
        $data = maybe_unserialize($data);

        // Get request status
        $status = get_post_meta($post->ID, 'bc_request_status', true);
        switch ($status) {
            case 'pending':
                $status_text = __('Pendent', 'bc-space-requests');
                $status_color = '#f0ad4e';
                break;
            case 'in-process':
                $status_text = __('En Procés', 'bc-space-requests');
                $status_color = '#5bc0de';
                break;
            case 'resolved':
                $status_text = __('Resolta', 'bc-space-requests');
                $status_color = '#5cb85c';
                break;
            default:
                $status_text = __('Unknown', 'bc-space-requests');
                $status_color = '#999';
        }

        // Load field labels from configuration file
        $field_labels = array();
        $field_groups = array();
        $labels_file = BC_REQUESTS_PLUGIN_DIR . 'includes/field-labels.php';

        if (file_exists($labels_file)) {
            include $labels_file;
            if (isset($bc_requests_field_labels) && is_array($bc_requests_field_labels)) {
                $field_labels = $bc_requests_field_labels;
            }
            if (isset($bc_requests_field_groups) && is_array($bc_requests_field_groups)) {
                $field_groups = $bc_requests_field_groups;
            }
        }

        // Start building HTML content
        $html = '<h1 style="color: #333;">' . esc_html($post->post_title) . ' <span style="color: #666; font-size: 0.8em;">(ID: ' . $post->ID . ')</span></h1>';
        $html .= '<p><strong>' . __('Status', 'bc-space-requests') . ':</strong> <span style="color: ' . $status_color . ';">' . $status_text . '</span></p>';

        // Add calendar assignments section
        $html .= self::get_calendar_assignments_html($post->ID);

        $html .= '<hr>';

        // If we have field groups, organize fields by groups
        if (!empty($field_groups)) {
            foreach ($field_groups as $group_name => $group_fields) {
                $group_html = '';
                $has_content = false;

                // Display fields in this group
                foreach ($group_fields as $field_key) {
                    // Get the value if it exists, otherwise use an empty string
                    $field_value = isset($data[$field_key]) ? $data[$field_key] : '';

                    // Skip empty fields
                    $is_empty = (is_array($field_value) && empty($field_value)) || (!is_array($field_value) && trim($field_value) === '');
                    if ($is_empty) {
                        continue;
                    }

                    // Get human-readable label from config or use the key
                    $label = isset($field_labels[$field_key]) ? $field_labels[$field_key] : $field_key;

                    $group_html .= '<tr>';
                    $group_html .= '<td width="30%" style="font-weight: bold;">' . esc_html($label) . ':</td>';

                    if (is_array($field_value)) {
                        $group_html .= '<td width="70%">' . esc_html(implode(', ', $field_value)) . '</td>';
                    } else {
                        $group_html .= '<td width="70%">' . esc_html($field_value) . '</td>';
                    }

                    $group_html .= '</tr>';
                    $has_content = true;
                }

                // Only add the group if it has content
                if ($has_content) {
                    $html .= '<h2>' . esc_html($group_name) . '</h2>';
                    $html .= '<table border="0" cellspacing="5" cellpadding="5">';
                    $html .= $group_html;
                    $html .= '</table>';
                    $html .= '<hr>';
                }
            }

            // Add a section for ungrouped fields
            $html .= '<h2>' . __('Altres camps', 'bc-space-requests') . '</h2>';
            $html .= '<table border="0" cellspacing="5" cellpadding="5">';

            // Find fields that aren't in any group
            $grouped_fields = array();
            foreach ($field_groups as $group_fields) {
                $grouped_fields = array_merge($grouped_fields, $group_fields);
            }

            // First, show all configured fields that aren't in any group
            foreach ($field_labels as $field_key => $field_label) {
                // Skip fields that are already in a group
                if (in_array($field_key, $grouped_fields)) {
                    continue;
                }

                // Get the value if it exists, otherwise use an empty string
                $field_value = isset($data[$field_key]) ? $data[$field_key] : '';

                $html .= '<tr>';
                $html .= '<td width="30%" style="font-weight: bold;">' . esc_html($field_label) . ':</td>';

                if (is_array($field_value)) {
                    $html .= '<td width="70%">' . esc_html(implode(', ', $field_value)) . '</td>';
                } else {
                    $html .= '<td width="70%">' . esc_html($field_value) . '</td>';
                }

                $html .= '</tr>';
            }

            // Then show any fields in the data that aren't configured
            foreach ($data as $key => $value) {
                // Skip internal CF7 fields, already grouped fields, and fields that are in the field_labels
                if (strpos($key, '_wpcf7') === 0 || in_array($key, $grouped_fields) || isset($field_labels[$key])) {
                    continue;
                }

                $html .= '<tr>';
                $html .= '<td width="30%" style="font-weight: bold;">' . esc_html($key) . ':</td>';

                if (is_array($value)) {
                    $html .= '<td width="70%">' . esc_html(implode(', ', $value)) . '</td>';
                } else {
                    $html .= '<td width="70%">' . esc_html($value) . '</td>';
                }

                $html .= '</tr>';
            }

            $html .= '</table>';
        } else {
            // No groups, just display all fields in a table
            $html .= '<table border="0" cellspacing="5" cellpadding="5">';

            // First, show all configured fields
            foreach ($field_labels as $field_key => $field_label) {
                // Get the value if it exists, otherwise use an empty string
                $field_value = isset($data[$field_key]) ? $data[$field_key] : '';

                $html .= '<tr>';
                $html .= '<td width="30%" style="font-weight: bold;">' . esc_html($field_label) . ':</td>';

                if (is_array($field_value)) {
                    $html .= '<td width="70%">' . esc_html(implode(', ', $field_value)) . '</td>';
                } else {
                    $html .= '<td width="70%">' . esc_html($field_value) . '</td>';
                }

                $html .= '</tr>';
            }

            // Then show any fields in the data that aren't configured
            foreach ($data as $key => $value) {
                // Skip internal CF7 fields and fields that are in the field_labels
                if (strpos($key, '_wpcf7') === 0 || isset($field_labels[$key])) {
                    continue;
                }

                $html .= '<tr>';
                $html .= '<td width="30%" style="font-weight: bold;">' . esc_html($key) . ':</td>';

                if (is_array($value)) {
                    $html .= '<td width="70%">' . esc_html(implode(', ', $value)) . '</td>';
                } else {
                    $html .= '<td width="70%">' . esc_html($value) . '</td>';
                }

                $html .= '</tr>';
            }

            $html .= '</table>';
        }

        // Add comments section
        $html .= self::get_comments_html($post->ID);

        return $html;
    }

    /**
     * Get calendar assignments HTML for PDF
     */
    private static function get_calendar_assignments_html($request_id) {
        // Check if BC_Requests_Calendar class exists
        if (!class_exists('BC_Requests_Calendar')) {
            return '';
        }

        // Get assignments for this request
        $assignments = BC_Requests_Calendar::get_request_assignments($request_id);

        if (empty($assignments)) {
            return '';
        }

        $html = '<div style="margin: 15px 0;">';
        $html .= '<h2>' . __('Calendar Assignments', 'bc-space-requests') . '</h2>';
        $html .= '<table border="1" cellspacing="0" cellpadding="5" style="width: 100%; border-collapse: collapse;">';
        $html .= '<thead>';
        $html .= '<tr style="background-color: #f2f2f2;">';
        $html .= '<th style="text-align: left;">' . __('Date', 'bc-space-requests') . '</th>';
        $html .= '<th style="text-align: left;">' . __('Space', 'bc-space-requests') . '</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';

        // Group assignments by date
        $assignments_by_date = array();
        foreach ($assignments as $assignment) {
            $date = $assignment->date;
            if (!isset($assignments_by_date[$date])) {
                $assignments_by_date[$date] = array();
            }
            $assignments_by_date[$date][] = $assignment;
        }

        // Sort dates
        ksort($assignments_by_date);

        // Display assignments
        foreach ($assignments_by_date as $date => $date_assignments) {
            $formatted_date = date_i18n(get_option('date_format'), strtotime($date));

            // Get spaces for this date
            $spaces = array();
            foreach ($date_assignments as $assignment) {
                $spaces[] = $assignment->space;
                $color = BC_Requests_Calendar::get_color_for_space($assignment->space);
            }

            $html .= '<tr>';
            $html .= '<td>' . esc_html($formatted_date) . '</td>';
            $html .= '<td>';

            foreach ($spaces as $index => $space) {
                $color = BC_Requests_Calendar::get_color_for_space($space);
                $html .= '<div style="margin: 2px 0; padding: 3px 6px; background-color: ' . esc_attr($color) . '; color: #fff; display: inline-block; border-radius: 3px;">';
                $html .= esc_html($space);
                $html .= '</div>';

                // Add a line break if not the last item
                if ($index < count($spaces) - 1) {
                    $html .= '<br>';
                }
            }

            $html .= '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        return $html;
    }

    /**
     * Get comments HTML for PDF
     */
    private static function get_comments_html($post_id) {
        $comments = get_comments(array(
            'post_id' => $post_id,
            'order' => 'ASC',
        ));

        if (empty($comments)) {
            return '';
        }

        $html = '<h2>' . __('Comments', 'bc-space-requests') . '</h2>';

        foreach ($comments as $comment) {
            $html .= '<div style="margin-bottom: 15px; padding: 10px; background-color: #f9f9f9; border-left: 3px solid #0073aa;">';

            // Comment author and date
            $html .= '<p style="margin: 0 0 5px 0; padding-bottom: 5px; border-bottom: 1px solid #eee;">';
            $html .= '<strong>' . esc_html($comment->comment_author) . '</strong> - ';
            $html .= '<em>' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($comment->comment_date)) . '</em>';
            $html .= '</p>';

            // Comment content
            $html .= '<p style="margin: 5px 0;">' . nl2br(esc_html($comment->comment_content)) . '</p>';

            // Comment attachments
            $files = get_comment_meta($comment->comment_ID, 'bc_comment_files', true);
            if (!empty($files)) {
                $html .= '<div style="margin-top: 5px; padding: 5px; background-color: #f5f5f5;">';
                $html .= '<p style="margin: 0 0 5px 0;"><strong>' . __('Arxius adjunts', 'bc-space-requests') . ':</strong></p>';
                $html .= '<ul style="margin: 0; padding-left: 20px;">';

                foreach ($files as $file) {
                    // Generate protected file URL
                    $file_url = home_url('?protected_file=' . $post_id . '/' . $file['saved_name']);

                    $html .= '<li>';
                    $html .= '<a href="' . esc_url($file_url) . '" target="_blank" style="color: #0073aa; text-decoration: underline;">';
                    $html .= esc_html($file['original_name']);
                    $html .= '</a>';

                    // Display user who uploaded the file if available
                    if (!empty($file['user_id'])) {
                        $user = get_userdata($file['user_id']);
                        if ($user) {
                            $html .= ' <span style="color: #666; font-size: 0.9em;">(' . __('Uploaded by', 'bc-space-requests') . ': ' . esc_html($user->display_name) . ')</span>';
                        }
                    }

                    $html .= '</li>';
                }

                $html .= '</ul>';
                $html .= '</div>';
            }

            $html .= '</div>';
        }

        return $html;
    }
}
